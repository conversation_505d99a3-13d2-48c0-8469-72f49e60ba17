#!/bin/bash

# CV Generator Production Server
# This script builds and runs the production version

echo "🏭 Building CV Generator for Production..."
echo ""

# Check if templ is installed
if ! command -v templ &> /dev/null; then
    echo "❌ Templ is not installed. Installing..."
    go install github.com/a-h/templ/cmd/templ@latest
    if [ $? -ne 0 ]; then
        echo "❌ Failed to install Templ. Please install manually:"
        echo "   go install github.com/a-h/templ/cmd/templ@latest"
        exit 1
    fi
    echo "✅ Templ installed successfully!"
fi

# Generate templates
echo "🔧 Generating Templ templates..."
templ generate
if [ $? -ne 0 ]; then
    echo "❌ Failed to generate templates"
    exit 1
fi

# Build the application
echo "🔨 Building application..."
go build -ldflags="-s -w" -o cv-generator .
if [ $? -ne 0 ]; then
    echo "❌ Build failed"
    exit 1
fi

echo "✅ Build successful!"
echo ""
echo "🚀 Starting production server..."
echo "🌐 Server available at: http://localhost:8080"
echo "🛑 Press Ctrl+C to stop"
echo ""

# Run the application
./cv-generator
