package templates

templ SimpleForm() {
	@BaseLayout("Create CV") {
		<div class="container">
			<h2>Create Your CV</h2>
			<form id="cv-form" method="post" action="/generate">
				<section class="form-section">
					<h3>Personal Information</h3>
					<div class="form-grid">
						<div class="form-group">
							<label for="full_name">Full Name *</label>
							<input type="text" id="full_name" name="full_name" required/>
						</div>
						<div class="form-group">
							<label for="email">Email *</label>
							<input type="email" id="email" name="email" required/>
						</div>
						<div class="form-group">
							<label for="phone">Phone</label>
							<input type="tel" id="phone" name="phone"/>
						</div>
						<div class="form-group">
							<label for="address">Address</label>
							<input type="text" id="address" name="address"/>
						</div>
						<div class="form-group">
							<label for="linkedin">LinkedIn</label>
							<input type="url" id="linkedin" name="linkedin"/>
						</div>
						<div class="form-group">
							<label for="github">GitHub</label>
							<input type="url" id="github" name="github"/>
						</div>
					</div>
					<div class="form-group">
						<label for="summary">Professional Summary</label>
						<textarea id="summary" name="summary" rows="4" placeholder="Brief summary of your professional background and goals"></textarea>
					</div>
				</section>

				<section class="form-section">
					<h3>Experience</h3>
					<div class="form-group">
						<label for="job_title">Job Title</label>
						<input type="text" id="job_title" name="job_title"/>
					</div>
					<div class="form-group">
						<label for="company">Company</label>
						<input type="text" id="company" name="company"/>
					</div>
					<div class="form-group">
						<label for="job_location">Location</label>
						<input type="text" id="job_location" name="job_location"/>
					</div>
					<div class="form-grid">
						<div class="form-group">
							<label for="start_date">Start Date</label>
							<input type="month" id="start_date" name="start_date"/>
						</div>
						<div class="form-group">
							<label for="end_date">End Date</label>
							<input type="month" id="end_date" name="end_date"/>
						</div>
					</div>
					<div class="form-group">
						<label class="checkbox-label">
							<input type="checkbox" name="current_job"/> Currently working here
						</label>
					</div>
					<div class="form-group">
						<label for="job_description">Description</label>
						<textarea id="job_description" name="job_description" rows="3"></textarea>
					</div>
				</section>

				<section class="form-section">
					<h3>Education</h3>
					<div class="form-group">
						<label for="degree">Degree</label>
						<input type="text" id="degree" name="degree"/>
					</div>
					<div class="form-group">
						<label for="institution">Institution</label>
						<input type="text" id="institution" name="institution"/>
					</div>
					<div class="form-group">
						<label for="edu_location">Location</label>
						<input type="text" id="edu_location" name="edu_location"/>
					</div>
					<div class="form-grid">
						<div class="form-group">
							<label for="edu_start_date">Start Date</label>
							<input type="month" id="edu_start_date" name="edu_start_date"/>
						</div>
						<div class="form-group">
							<label for="edu_end_date">End Date</label>
							<input type="month" id="edu_end_date" name="edu_end_date"/>
						</div>
					</div>
					<div class="form-group">
						<label for="gpa">GPA</label>
						<input type="text" id="gpa" name="gpa"/>
					</div>
				</section>

				<section class="form-section">
					<h3>Skills</h3>
					<div class="form-group">
						<label for="skills">Skills (comma-separated)</label>
						<textarea id="skills" name="skills" rows="3" placeholder="JavaScript, Python, React, Node.js, Docker"></textarea>
					</div>
				</section>

				<section class="form-section">
					<h3>Projects</h3>
					<div class="form-group">
						<label for="project_name">Project Name</label>
						<input type="text" id="project_name" name="project_name"/>
					</div>
					<div class="form-group">
						<label for="project_description">Description</label>
						<textarea id="project_description" name="project_description" rows="3"></textarea>
					</div>
					<div class="form-group">
						<label for="project_technologies">Technologies</label>
						<input type="text" id="project_technologies" name="project_technologies" placeholder="React, Node.js, MongoDB"/>
					</div>
					<div class="form-group">
						<label for="project_url">Project URL</label>
						<input type="url" id="project_url" name="project_url"/>
					</div>
				</section>

				<div class="form-actions">
					<button type="button" class="btn btn-secondary" onclick="previewCV()">Preview</button>
					<button type="button" class="btn btn-primary" onclick="generatePDF()">Generate PDF</button>
					<button type="submit" class="btn btn-primary">Generate HTML</button>
				</div>
			</form>
		</div>
		
		<div class="preview-container" id="preview-container" style="display: none;">
			<div class="preview-header">
				<h3>CV Preview</h3>
				<button type="button" class="btn btn-secondary" onclick="closePreview()">Close</button>
			</div>
			<div class="preview-content" id="preview-content">
				<!-- Preview will be loaded here -->
			</div>
		</div>
	}
}
