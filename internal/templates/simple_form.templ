package templates

import "cv-generator/internal/models"

templ SimpleForm(templates []models.CVTemplate) {
	@BaseLayout("Create CV") {
		<div class="form-page">
			<div class="form-header">
				<div class="container">
					<h1 class="form-title">Create Your Professional CV</h1>
					<p class="form-subtitle">Fill out the form below to generate your professional CV in minutes</p>
					<div class="progress-bar">
						<div class="progress-fill" id="progress-fill"></div>
					</div>
					<div class="progress-steps">
						<div class="step active" data-step="1">
							<div class="step-number">1</div>
							<div class="step-label">Template</div>
						</div>
						<div class="step" data-step="2">
							<div class="step-number">2</div>
							<div class="step-label">Personal</div>
						</div>
						<div class="step" data-step="3">
							<div class="step-number">3</div>
							<div class="step-label">Experience</div>
						</div>
						<div class="step" data-step="4">
							<div class="step-number">4</div>
							<div class="step-label">Education</div>
						</div>
						<div class="step" data-step="5">
							<div class="step-number">5</div>
							<div class="step-label">Skills</div>
						</div>
						<div class="step" data-step="6">
							<div class="step-number">6</div>
							<div class="step-label">Projects</div>
						</div>
					</div>
				</div>
			</div>

			<div class="form-container">
				<div class="form-sidebar">
					<div class="sidebar-content">
						<h3>Tips for a Great CV</h3>
						<div class="tip-card">
							<i class="fas fa-lightbulb"></i>
							<div>
								<h4>Keep it Concise</h4>
								<p>Aim for 1-2 pages maximum. Highlight your most relevant experiences.</p>
							</div>
						</div>
						<div class="tip-card">
							<i class="fas fa-target"></i>
							<div>
								<h4>Tailor Your Content</h4>
								<p>Customize your CV for each job application to match the requirements.</p>
							</div>
						</div>
						<div class="tip-card">
							<i class="fas fa-spell-check"></i>
							<div>
								<h4>Proofread Carefully</h4>
								<p>Check for spelling and grammar errors. A polished CV makes a great impression.</p>
							</div>
						</div>
					</div>
				</div>

				<div class="form-main">
					<form id="cv-form" method="post" action="/generate">
						<div class="form-step active" data-step="1">
							<div class="step-header">
								<h2><i class="fas fa-palette"></i> Choose Your Template</h2>
								<p>Select a professional template that matches your style and industry</p>
							</div>
							@TemplateSelector(templates, "modern")
						</div>

						<div class="form-step" data-step="2">
							<div class="step-header">
								<h2><i class="fas fa-user"></i> Personal Information</h2>
								<p>Let's start with your basic information</p>
							</div>
							<div class="form-grid">
								<div class="form-group">
									<label for="full_name">Full Name *</label>
									<input type="text" id="full_name" name="full_name" required placeholder="John Doe"/>
									<div class="field-hint">Your full name as you want it to appear on your CV</div>
								</div>
								<div class="form-group">
									<label for="email">Email Address *</label>
									<input type="email" id="email" name="email" required placeholder="<EMAIL>"/>
									<div class="field-hint">Professional email address</div>
								</div>
								<div class="form-group">
									<label for="phone">Phone Number</label>
									<input type="tel" id="phone" name="phone" placeholder="+****************"/>
									<div class="field-hint">Include country code if applying internationally</div>
								</div>
								<div class="form-group">
									<label for="address">Location</label>
									<input type="text" id="address" name="address" placeholder="City, State/Country"/>
									<div class="field-hint">City and country/state where you're based</div>
								</div>
								<div class="form-group">
									<label for="linkedin">LinkedIn Profile</label>
									<input type="url" id="linkedin" name="linkedin" placeholder="https://linkedin.com/in/johndoe"/>
									<div class="field-hint">Your LinkedIn profile URL</div>
								</div>
								<div class="form-group">
									<label for="github">GitHub Profile</label>
									<input type="url" id="github" name="github" placeholder="https://github.com/johndoe"/>
									<div class="field-hint">Your GitHub profile (for technical roles)</div>
								</div>
							</div>
							<div class="form-group">
								<label for="summary">Professional Summary</label>
								<textarea id="summary" name="summary" rows="4" placeholder="A brief 2-3 sentence summary highlighting your key skills, experience, and career objectives..."></textarea>
								<div class="field-hint">A compelling summary that showcases your value proposition</div>
							</div>
						</div>

						<div class="form-step" data-step="2">
							<div class="step-header">
								<h2><i class="fas fa-briefcase"></i> Professional Experience</h2>
								<p>Tell us about your most recent or relevant work experience</p>
							</div>
							<div class="form-grid">
								<div class="form-group">
									<label for="job_title">Job Title *</label>
									<input type="text" id="job_title" name="job_title" placeholder="Software Engineer"/>
									<div class="field-hint">Your official job title</div>
								</div>
								<div class="form-group">
									<label for="company">Company *</label>
									<input type="text" id="company" name="company" placeholder="Tech Corp Inc."/>
									<div class="field-hint">Company or organization name</div>
								</div>
								<div class="form-group">
									<label for="job_location">Location</label>
									<input type="text" id="job_location" name="job_location" placeholder="San Francisco, CA"/>
									<div class="field-hint">City where you worked</div>
								</div>
								<div class="form-group">
									<label for="start_date">Start Date</label>
									<input type="month" id="start_date" name="start_date"/>
									<div class="field-hint">When you started this position</div>
								</div>
								<div class="form-group">
									<label for="end_date">End Date</label>
									<input type="month" id="end_date" name="end_date"/>
									<div class="field-hint">Leave blank if current position</div>
								</div>
								<div class="form-group checkbox-group">
									<label class="checkbox-label">
										<input type="checkbox" name="current_job" id="current_job"/>
										<span class="checkmark"></span>
										I currently work here
									</label>
								</div>
							</div>
							<div class="form-group">
								<label for="job_description">Job Description</label>
								<textarea id="job_description" name="job_description" rows="4" placeholder="Describe your key responsibilities, achievements, and impact in this role..."></textarea>
								<div class="field-hint">Focus on achievements and quantifiable results</div>
							</div>
						</div>

						<div class="form-step" data-step="4">
							<div class="step-header">
								<h2><i class="fas fa-graduation-cap"></i> Education</h2>
								<p>Add your educational background</p>
							</div>
							<div class="form-grid">
								<div class="form-group">
									<label for="degree">Degree</label>
									<input type="text" id="degree" name="degree" placeholder="Bachelor of Science in Computer Science"/>
									<div class="field-hint">Your degree title</div>
								</div>
								<div class="form-group">
									<label for="institution">Institution</label>
									<input type="text" id="institution" name="institution" placeholder="University of Technology"/>
									<div class="field-hint">School or university name</div>
								</div>
								<div class="form-group">
									<label for="edu_location">Location</label>
									<input type="text" id="edu_location" name="edu_location" placeholder="Boston, MA"/>
									<div class="field-hint">City where you studied</div>
								</div>
								<div class="form-group">
									<label for="edu_start_date">Start Date</label>
									<input type="month" id="edu_start_date" name="edu_start_date"/>
									<div class="field-hint">When you started</div>
								</div>
								<div class="form-group">
									<label for="edu_end_date">End Date</label>
									<input type="month" id="edu_end_date" name="edu_end_date"/>
									<div class="field-hint">Graduation date</div>
								</div>
								<div class="form-group">
									<label for="gpa">GPA (Optional)</label>
									<input type="text" id="gpa" name="gpa" placeholder="3.8/4.0"/>
									<div class="field-hint">Include if 3.5 or higher</div>
								</div>
							</div>
						</div>

						<div class="form-step" data-step="5">
							<div class="step-header">
								<h2><i class="fas fa-cogs"></i> Skills</h2>
								<p>List your technical and professional skills</p>
							</div>
							<div class="form-group">
								<label for="skills">Skills (comma-separated)</label>
								<textarea id="skills" name="skills" rows="4" placeholder="JavaScript, Python, React, Node.js, Docker, AWS, Project Management, Team Leadership"></textarea>
								<div class="field-hint">List your most relevant skills, separated by commas</div>
							</div>
						</div>

						<div class="form-step" data-step="6">
							<div class="step-header">
								<h2><i class="fas fa-project-diagram"></i> Projects</h2>
								<p>Showcase your best project or portfolio piece</p>
							</div>
							<div class="form-grid">
								<div class="form-group">
									<label for="project_name">Project Name</label>
									<input type="text" id="project_name" name="project_name" placeholder="E-commerce Platform"/>
									<div class="field-hint">Name of your project</div>
								</div>
								<div class="form-group">
									<label for="project_url">Project URL</label>
									<input type="url" id="project_url" name="project_url" placeholder="https://myproject.com"/>
									<div class="field-hint">Live demo or repository link</div>
								</div>
							</div>
							<div class="form-group">
								<label for="project_description">Project Description</label>
								<textarea id="project_description" name="project_description" rows="4" placeholder="Describe what the project does, your role, and the impact it had..."></textarea>
								<div class="field-hint">Focus on your contribution and the results achieved</div>
							</div>
							<div class="form-group">
								<label for="project_technologies">Technologies Used</label>
								<input type="text" id="project_technologies" name="project_technologies" placeholder="React, Node.js, MongoDB, AWS"/>
								<div class="field-hint">Technologies and tools used in this project</div>
							</div>
						</div>

						<div class="form-navigation">
							<button type="button" class="btn btn-secondary" id="prev-btn" onclick="previousStep()" style="display: none;">
								<i class="fas fa-arrow-left"></i> Previous
							</button>
							<button type="button" class="btn btn-primary" id="next-btn" onclick="nextStep()">
								Next <i class="fas fa-arrow-right"></i>
							</button>
							<button type="button" class="btn btn-outline" id="preview-btn" onclick="previewCV()" style="display: none;">
								<i class="fas fa-eye"></i> Preview CV
							</button>
						</div>

						<div class="form-actions" id="final-actions" style="display: none;">
							<button type="button" class="btn btn-secondary" onclick="previewCV()">
								<i class="fas fa-eye"></i> Preview
							</button>
							<button type="button" class="btn btn-primary" onclick="generatePDF()">
								<i class="fas fa-download"></i> Generate PDF
							</button>
							<button type="submit" class="btn btn-primary">
								<i class="fas fa-file-code"></i> Generate HTML
							</button>
						</div>
					</form>
				</div>
			</div>

			<div class="preview-container" id="preview-container" style="display: none;">
				<div class="preview-header">
					<h3>CV Preview</h3>
					<button type="button" class="btn btn-secondary" onclick="closePreview()">
						<i class="fas fa-times"></i> Close
					</button>
				</div>
				<div class="preview-content" id="preview-content">
					<!-- Preview will be loaded here -->
				</div>
			</div>
		</div>
	}
}
