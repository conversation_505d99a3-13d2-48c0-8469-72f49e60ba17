package templates

import "cv-generator/internal/models"

templ CVForm(cv models.CV) {
	@BaseLayout("Create CV") {
		<div class="form-container">
			<div class="form-sidebar">
				<h2>CV Builder</h2>
				<nav class="form-nav">
					<a href="#personal-info" class="nav-item active">Personal Info</a>
					<a href="#experience" class="nav-item">Experience</a>
					<a href="#education" class="nav-item">Education</a>
					<a href="#skills" class="nav-item">Skills</a>
					<a href="#projects" class="nav-item">Projects</a>
					<a href="#certifications" class="nav-item">Certifications</a>
					<a href="#languages" class="nav-item">Languages</a>
				</nav>
				<div class="form-actions">
					<button type="button" class="btn btn-secondary" onclick="previewCV()">Preview</button>
					<button type="button" class="btn btn-primary" onclick="generatePDF()">Generate PDF</button>
				</div>
			</div>
			
			<div class="form-content">
				<form id="cv-form" method="post" action="/generate">
					@PersonalInfoForm(cv.PersonalInfo)
					@ExperienceForm(cv.Experiences)
					@EducationForm(cv.Education)
					@SkillsForm(cv.Skills)
					@ProjectsForm(cv.Projects)
					@CertificationsForm(cv.Certifications)
					@LanguagesForm(cv.Languages)
				</form>
			</div>
			
			<div class="preview-container" id="preview-container" style="display: none;">
				<div class="preview-header">
					<h3>CV Preview</h3>
					<button type="button" class="btn btn-secondary" onclick="closePreview()">Close</button>
				</div>
				<div class="preview-content" id="preview-content">
					<!-- Preview will be loaded here -->
				</div>
			</div>
		</div>
	}
}

templ PersonalInfoForm(info models.PersonalInfo) {
	<section id="personal-info" class="form-section">
		<h3>Personal Information</h3>
		<div class="form-grid">
			<div class="form-group">
				<label for="full_name">Full Name *</label>
				<input type="text" id="full_name" name="full_name" value={ info.FullName } required/>
			</div>
			<div class="form-group">
				<label for="email">Email *</label>
				<input type="email" id="email" name="email" value={ info.Email } required/>
			</div>
			<div class="form-group">
				<label for="phone">Phone</label>
				<input type="tel" id="phone" name="phone" value={ info.Phone }/>
			</div>
			<div class="form-group">
				<label for="address">Address</label>
				<input type="text" id="address" name="address" value={ info.Address }/>
			</div>
			<div class="form-group">
				<label for="linkedin">LinkedIn</label>
				<input type="url" id="linkedin" name="linkedin" value={ info.LinkedIn }/>
			</div>
			<div class="form-group">
				<label for="github">GitHub</label>
				<input type="url" id="github" name="github" value={ info.GitHub }/>
			</div>
			<div class="form-group">
				<label for="website">Website</label>
				<input type="url" id="website" name="website" value={ info.Website }/>
			</div>
		</div>
		<div class="form-group">
			<label for="summary">Professional Summary</label>
			<textarea id="summary" name="summary" rows="4" placeholder="Brief summary of your professional background and goals">{ info.Summary }</textarea>
		</div>
	</section>
}

templ ExperienceForm(experiences []models.Experience) {
	<section id="experience" class="form-section">
		<div class="section-header">
			<h3>Professional Experience</h3>
			<button type="button" class="btn btn-secondary" onclick="addExperience()">Add Experience</button>
		</div>
		<div id="experience-list">
			for i, exp := range experiences {
				@ExperienceFormItem(exp, i)
			}
		</div>
	</section>
}

templ ExperienceFormItem(exp models.Experience, index int) {
	<div class="form-item" data-index={ templ.JSONString(index) }>
		<div class="item-header">
			<h4>Experience { templ.JSONString(index + 1) }</h4>
			<button type="button" class="btn btn-danger btn-sm" onclick="removeItem(this)">Remove</button>
		</div>
		<div class="form-grid">
			<div class="form-group">
				<label for={ "job_title_" + templ.JSONString(index) }>Job Title *</label>
				<input type="text" id={ "job_title_" + templ.JSONString(index) } name={ "experiences[" + templ.JSONString(index) + "].job_title" } value={ exp.JobTitle } required/>
			</div>
			<div class="form-group">
				<label for={ "company_" + templ.JSONString(index) }>Company *</label>
				<input type="text" id={ "company_" + templ.JSONString(index) } name={ "experiences[" + templ.JSONString(index) + "].company" } value={ exp.Company } required/>
			</div>
			<div class="form-group">
				<label for={ "location_" + templ.JSONString(index) }>Location</label>
				<input type="text" id={ "location_" + templ.JSONString(index) } name={ "experiences[" + templ.JSONString(index) + "].location" } value={ exp.Location }/>
			</div>
			<div class="form-group">
				<label for={ "start_date_" + templ.JSONString(index) }>Start Date *</label>
				<input type="month" id={ "start_date_" + templ.JSONString(index) } name={ "experiences[" + templ.JSONString(index) + "].start_date" } value={ exp.StartDate.Format("2006-01") } required/>
			</div>
			<div class="form-group">
				<label for={ "end_date_" + templ.JSONString(index) }>End Date</label>
				<input type="month" id={ "end_date_" + templ.JSONString(index) } name={ "experiences[" + templ.JSONString(index) + "].end_date" } 
					if exp.EndDate != nil {
						value={ exp.EndDate.Format("2006-01") }
					}
				/>
			</div>
			<div class="form-group">
				<label class="checkbox-label">
					<input type="checkbox" name={ "experiences[" + templ.JSONString(index) + "].current" } 
						if exp.Current {
							checked
						}
					/> Currently working here
				</label>
			</div>
		</div>
		<div class="form-group">
			<label for={ "description_" + templ.JSONString(index) }>Description</label>
			<textarea id={ "description_" + templ.JSONString(index) } name={ "experiences[" + templ.JSONString(index) + "].description" } rows="3">{ exp.Description }</textarea>
		</div>
		<div class="form-group">
			<label for={ "achievements_" + templ.JSONString(index) }>Key Achievements (one per line)</label>
			<textarea id={ "achievements_" + templ.JSONString(index) } name={ "experiences[" + templ.JSONString(index) + "].achievements" } rows="3" placeholder="• Increased sales by 25%&#10;• Led a team of 5 developers&#10;• Implemented new system that reduced costs by 15%">
				for _, achievement := range exp.Achievements {
					{ achievement }
				}
			</textarea>
		</div>
	</div>
}

templ EducationForm(education []models.Education) {
	<section id="education" class="form-section">
		<div class="section-header">
			<h3>Education</h3>
			<button type="button" class="btn btn-secondary" onclick="addEducation()">Add Education</button>
		</div>
		<div id="education-list">
			for i, edu := range education {
				@EducationFormItem(edu, i)
			}
		</div>
	</section>
}

templ EducationFormItem(edu models.Education, index int) {
	<div class="form-item" data-index={ templ.JSONString(index) }>
		<div class="item-header">
			<h4>Education { templ.JSONString(index + 1) }</h4>
			<button type="button" class="btn btn-danger btn-sm" onclick="removeItem(this)">Remove</button>
		</div>
		<div class="form-grid">
			<div class="form-group">
				<label for={ "degree_" + templ.JSONString(index) }>Degree *</label>
				<input type="text" id={ "degree_" + templ.JSONString(index) } name={ "education[" + templ.JSONString(index) + "].degree" } value={ edu.Degree } required/>
			</div>
			<div class="form-group">
				<label for={ "institution_" + templ.JSONString(index) }>Institution *</label>
				<input type="text" id={ "institution_" + templ.JSONString(index) } name={ "education[" + templ.JSONString(index) + "].institution" } value={ edu.Institution } required/>
			</div>
			<div class="form-group">
				<label for={ "edu_location_" + templ.JSONString(index) }>Location</label>
				<input type="text" id={ "edu_location_" + templ.JSONString(index) } name={ "education[" + templ.JSONString(index) + "].location" } value={ edu.Location }/>
			</div>
			<div class="form-group">
				<label for={ "edu_start_date_" + templ.JSONString(index) }>Start Date *</label>
				<input type="month" id={ "edu_start_date_" + templ.JSONString(index) } name={ "education[" + templ.JSONString(index) + "].start_date" } value={ edu.StartDate.Format("2006-01") } required/>
			</div>
			<div class="form-group">
				<label for={ "edu_end_date_" + templ.JSONString(index) }>End Date</label>
				<input type="month" id={ "edu_end_date_" + templ.JSONString(index) } name={ "education[" + templ.JSONString(index) + "].end_date" }
					if edu.EndDate != nil {
						value={ edu.EndDate.Format("2006-01") }
					}
				/>
			</div>
			<div class="form-group">
				<label for={ "gpa_" + templ.JSONString(index) }>GPA</label>
				<input type="text" id={ "gpa_" + templ.JSONString(index) } name={ "education[" + templ.JSONString(index) + "].gpa" } value={ edu.GPA }/>
			</div>
		</div>
		<div class="form-group">
			<label class="checkbox-label">
				<input type="checkbox" name={ "education[" + templ.JSONString(index) + "].current" }
					if edu.Current {
						checked
					}
				/> Currently studying here
			</label>
		</div>
		<div class="form-group">
			<label for={ "edu_description_" + templ.JSONString(index) }>Description</label>
			<textarea id={ "edu_description_" + templ.JSONString(index) } name={ "education[" + templ.JSONString(index) + "].description" } rows="2">{ edu.Description }</textarea>
		</div>
	</div>
}

templ SkillsForm(skills []models.Skill) {
	<section id="skills" class="form-section">
		<div class="section-header">
			<h3>Skills</h3>
			<button type="button" class="btn btn-secondary" onclick="addSkill()">Add Skill</button>
		</div>
		<div id="skills-list">
			for i, skill := range skills {
				@SkillFormItem(skill, i)
			}
		</div>
	</section>
}

templ SkillFormItem(skill models.Skill, index int) {
	<div class="form-item skill-item" data-index={ templ.JSONString(index) }>
		<div class="form-grid">
			<div class="form-group">
				<label for={ "skill_name_" + templ.JSONString(index) }>Skill *</label>
				<input type="text" id={ "skill_name_" + templ.JSONString(index) } name={ "skills[" + templ.JSONString(index) + "].name" } value={ skill.Name } required/>
			</div>
			<div class="form-group">
				<label for={ "skill_category_" + templ.JSONString(index) }>Category</label>
				<select id={ "skill_category_" + templ.JSONString(index) } name={ "skills[" + templ.JSONString(index) + "].category" }>
					<option value="Programming"
						if skill.Category == "Programming" {
							selected
						}
					>Programming</option>
					<option value="Languages"
						if skill.Category == "Languages" {
							selected
						}
					>Languages</option>
					<option value="Tools"
						if skill.Category == "Tools" {
							selected
						}
					>Tools</option>
					<option value="Frameworks"
						if skill.Category == "Frameworks" {
							selected
						}
					>Frameworks</option>
					<option value="Other"
						if skill.Category == "Other" {
							selected
						}
					>Other</option>
				</select>
			</div>
			<div class="form-group">
				<label for={ "skill_proficiency_" + templ.JSONString(index) }>Proficiency</label>
				<select id={ "skill_proficiency_" + templ.JSONString(index) } name={ "skills[" + templ.JSONString(index) + "].proficiency" }>
					<option value="Beginner"
						if skill.Proficiency == "Beginner" {
							selected
						}
					>Beginner</option>
					<option value="Intermediate"
						if skill.Proficiency == "Intermediate" {
							selected
						}
					>Intermediate</option>
					<option value="Advanced"
						if skill.Proficiency == "Advanced" {
							selected
						}
					>Advanced</option>
					<option value="Expert"
						if skill.Proficiency == "Expert" {
							selected
						}
					>Expert</option>
				</select>
			</div>
			<div class="form-group">
				<button type="button" class="btn btn-danger btn-sm" onclick="removeItem(this)">Remove</button>
			</div>
		</div>
	</div>
}

templ ProjectsForm(projects []models.Project) {
	<section id="projects" class="form-section">
		<div class="section-header">
			<h3>Projects</h3>
			<button type="button" class="btn btn-secondary" onclick="addProject()">Add Project</button>
		</div>
		<div id="projects-list">
			for i, project := range projects {
				@ProjectFormItem(project, i)
			}
		</div>
	</section>
}

templ ProjectFormItem(project models.Project, index int) {
	<div class="form-item" data-index={ templ.JSONString(index) }>
		<div class="item-header">
			<h4>Project { templ.JSONString(index + 1) }</h4>
			<button type="button" class="btn btn-danger btn-sm" onclick="removeItem(this)">Remove</button>
		</div>
		<div class="form-grid">
			<div class="form-group">
				<label for={ "project_name_" + templ.JSONString(index) }>Project Name *</label>
				<input type="text" id={ "project_name_" + templ.JSONString(index) } name={ "projects[" + templ.JSONString(index) + "].name" } value={ project.Name } required/>
			</div>
			<div class="form-group">
				<label for={ "project_url_" + templ.JSONString(index) }>Live URL</label>
				<input type="url" id={ "project_url_" + templ.JSONString(index) } name={ "projects[" + templ.JSONString(index) + "].url" } value={ project.URL }/>
			</div>
			<div class="form-group">
				<label for={ "project_github_" + templ.JSONString(index) }>GitHub URL</label>
				<input type="url" id={ "project_github_" + templ.JSONString(index) } name={ "projects[" + templ.JSONString(index) + "].github_url" } value={ project.GitHubURL }/>
			</div>
			<div class="form-group">
				<label for={ "project_start_date_" + templ.JSONString(index) }>Start Date *</label>
				<input type="month" id={ "project_start_date_" + templ.JSONString(index) } name={ "projects[" + templ.JSONString(index) + "].start_date" } value={ project.StartDate.Format("2006-01") } required/>
			</div>
			<div class="form-group">
				<label for={ "project_end_date_" + templ.JSONString(index) }>End Date</label>
				<input type="month" id={ "project_end_date_" + templ.JSONString(index) } name={ "projects[" + templ.JSONString(index) + "].end_date" }
					if project.EndDate != nil {
						value={ project.EndDate.Format("2006-01") }
					}
				/>
			</div>
			<div class="form-group">
				<label class="checkbox-label">
					<input type="checkbox" name={ "projects[" + templ.JSONString(index) + "].current" }
						if project.Current {
							checked
						}
					/> Currently working on this
				</label>
			</div>
		</div>
		<div class="form-group">
			<label for={ "project_description_" + templ.JSONString(index) }>Description *</label>
			<textarea id={ "project_description_" + templ.JSONString(index) } name={ "projects[" + templ.JSONString(index) + "].description" } rows="3" required>{ project.Description }</textarea>
		</div>
		<div class="form-group">
			<label for={ "project_technologies_" + templ.JSONString(index) }>Technologies (comma-separated)</label>
			<input type="text" id={ "project_technologies_" + templ.JSONString(index) } name={ "projects[" + templ.JSONString(index) + "].technologies" }
				value={
					templ.JSONString(func() string {
						result := ""
						for i, tech := range project.Technologies {
							if i > 0 {
								result += ", "
							}
							result += tech
						}
						return result
					}())
				}
				placeholder="React, Node.js, MongoDB, Docker"
			/>
		</div>
	</div>
}

templ CertificationsForm(certifications []models.Certification) {
	<section id="certifications" class="form-section">
		<div class="section-header">
			<h3>Certifications</h3>
			<button type="button" class="btn btn-secondary" onclick="addCertification()">Add Certification</button>
		</div>
		<div id="certifications-list">
			for i, cert := range certifications {
				@CertificationFormItem(cert, i)
			}
		</div>
	</section>
}

templ CertificationFormItem(cert models.Certification, index int) {
	<div class="form-item" data-index={ templ.JSONString(index) }>
		<div class="item-header">
			<h4>Certification { templ.JSONString(index + 1) }</h4>
			<button type="button" class="btn btn-danger btn-sm" onclick="removeItem(this)">Remove</button>
		</div>
		<div class="form-grid">
			<div class="form-group">
				<label for={ "cert_name_" + templ.JSONString(index) }>Certification Name *</label>
				<input type="text" id={ "cert_name_" + templ.JSONString(index) } name={ "certifications[" + templ.JSONString(index) + "].name" } value={ cert.Name } required/>
			</div>
			<div class="form-group">
				<label for={ "cert_issuer_" + templ.JSONString(index) }>Issuer *</label>
				<input type="text" id={ "cert_issuer_" + templ.JSONString(index) } name={ "certifications[" + templ.JSONString(index) + "].issuer" } value={ cert.Issuer } required/>
			</div>
			<div class="form-group">
				<label for={ "cert_issue_date_" + templ.JSONString(index) }>Issue Date *</label>
				<input type="month" id={ "cert_issue_date_" + templ.JSONString(index) } name={ "certifications[" + templ.JSONString(index) + "].issue_date" } value={ cert.IssueDate.Format("2006-01") } required/>
			</div>
			<div class="form-group">
				<label for={ "cert_expiry_date_" + templ.JSONString(index) }>Expiry Date</label>
				<input type="month" id={ "cert_expiry_date_" + templ.JSONString(index) } name={ "certifications[" + templ.JSONString(index) + "].expiry_date" }
					if cert.ExpiryDate != nil {
						value={ cert.ExpiryDate.Format("2006-01") }
					}
				/>
			</div>
			<div class="form-group">
				<label for={ "cert_credential_id_" + templ.JSONString(index) }>Credential ID</label>
				<input type="text" id={ "cert_credential_id_" + templ.JSONString(index) } name={ "certifications[" + templ.JSONString(index) + "].credential_id" } value={ cert.CredentialID }/>
			</div>
			<div class="form-group">
				<label for={ "cert_url_" + templ.JSONString(index) }>Verification URL</label>
				<input type="url" id={ "cert_url_" + templ.JSONString(index) } name={ "certifications[" + templ.JSONString(index) + "].url" } value={ cert.URL }/>
			</div>
		</div>
	</div>
}

templ LanguagesForm(languages []models.Language) {
	<section id="languages" class="form-section">
		<div class="section-header">
			<h3>Languages</h3>
			<button type="button" class="btn btn-secondary" onclick="addLanguage()">Add Language</button>
		</div>
		<div id="languages-list">
			for i, lang := range languages {
				@LanguageFormItem(lang, i)
			}
		</div>
	</section>
}

templ LanguageFormItem(lang models.Language, index int) {
	<div class="form-item language-item" data-index={ templ.JSONString(index) }>
		<div class="form-grid">
			<div class="form-group">
				<label for={ "lang_name_" + templ.JSONString(index) }>Language *</label>
				<input type="text" id={ "lang_name_" + templ.JSONString(index) } name={ "languages[" + templ.JSONString(index) + "].name" } value={ lang.Name } required/>
			</div>
			<div class="form-group">
				<label for={ "lang_proficiency_" + templ.JSONString(index) }>Proficiency</label>
				<select id={ "lang_proficiency_" + templ.JSONString(index) } name={ "languages[" + templ.JSONString(index) + "].proficiency" }>
					<option value="Native"
						if lang.Proficiency == "Native" {
							selected
						}
					>Native</option>
					<option value="Fluent"
						if lang.Proficiency == "Fluent" {
							selected
						}
					>Fluent</option>
					<option value="Conversational"
						if lang.Proficiency == "Conversational" {
							selected
						}
					>Conversational</option>
					<option value="Basic"
						if lang.Proficiency == "Basic" {
							selected
						}
					>Basic</option>
				</select>
			</div>
			<div class="form-group">
				<button type="button" class="btn btn-danger btn-sm" onclick="removeItem(this)">Remove</button>
			</div>
		</div>
	</div>
}
