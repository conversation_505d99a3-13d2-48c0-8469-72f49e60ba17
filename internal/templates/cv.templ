package templates

import "cv-generator/internal/models"

// Helper function to group skills by category
func groupSkillsByCategory(skills []models.Skill) map[string][]models.Skill {
	grouped := make(map[string][]models.Skill)
	for _, skill := range skills {
		category := skill.Category
		if category == "" {
			category = "Other"
		}
		grouped[category] = append(grouped[category], skill)
	}
	return grouped
}

templ CVComplete(cv models.CV) {
	<div class="cv-container">
		@CVHeader(cv.PersonalInfo)
		@ExperienceSection(cv.Experiences)
		@EducationSection(cv.Education)
		@SkillsSection(cv.Skills)
		@ProjectsSection(cv.Projects)
		if len(cv.Certifications) > 0 {
			@CertificationsSection(cv.Certifications)
		}
		if len(cv.Languages) > 0 {
			@LanguagesSection(cv.Languages)
		}
	</div>
}

templ CVHeader(info models.PersonalInfo) {
	<header class="cv-header">
		<div class="header-main">
			<h1 class="name">{ info.FullName }</h1>
			if info.Summary != "" {
				<p class="summary">{ info.Summary }</p>
			}
		</div>
		<div class="contact-info">
			if info.Email != "" {
				<div class="contact-item">
					<i class="fas fa-envelope"></i>
					<span>{ info.Email }</span>
				</div>
			}
			if info.Phone != "" {
				<div class="contact-item">
					<i class="fas fa-phone"></i>
					<span>{ info.Phone }</span>
				</div>
			}
			if info.Address != "" {
				<div class="contact-item">
					<i class="fas fa-map-marker-alt"></i>
					<span>{ info.Address }</span>
				</div>
			}
			if info.LinkedIn != "" {
				<div class="contact-item">
					<i class="fab fa-linkedin"></i>
					<a href={ templ.URL(info.LinkedIn) } target="_blank">LinkedIn</a>
				</div>
			}
			if info.GitHub != "" {
				<div class="contact-item">
					<i class="fab fa-github"></i>
					<a href={ templ.URL(info.GitHub) } target="_blank">GitHub</a>
				</div>
			}
			if info.Website != "" {
				<div class="contact-item">
					<i class="fas fa-globe"></i>
					<a href={ templ.URL(info.Website) } target="_blank">Website</a>
				</div>
			}
		</div>
	</header>
}

templ ExperienceSection(experiences []models.Experience) {
	if len(experiences) > 0 {
		<section class="cv-section">
			<h2 class="section-title">Professional Experience</h2>
			for _, exp := range experiences {
				@ExperienceItem(exp)
			}
		</section>
	}
}

templ ExperienceItem(exp models.Experience) {
	<div class="experience-item">
		<div class="item-header">
			<h3 class="job-title">{ exp.JobTitle }</h3>
			<div class="date-range">{ models.FormatDateRange(exp.StartDate, exp.EndDate, exp.Current) }</div>
		</div>
		<div class="company-location">
			<span class="company">{ exp.Company }</span>
			if exp.Location != "" {
				<span class="location"> • { exp.Location }</span>
			}
		</div>
		if exp.Description != "" {
			<p class="description">{ exp.Description }</p>
		}
		if len(exp.Achievements) > 0 {
			<ul class="achievements">
				for _, achievement := range exp.Achievements {
					<li>{ achievement }</li>
				}
			</ul>
		}
	</div>
}

templ EducationSection(education []models.Education) {
	if len(education) > 0 {
		<section class="cv-section">
			<h2 class="section-title">Education</h2>
			for _, edu := range education {
				@EducationItem(edu)
			}
		</section>
	}
}

templ EducationItem(edu models.Education) {
	<div class="education-item">
		<div class="item-header">
			<h3 class="degree">{ edu.Degree }</h3>
			<div class="date-range">{ models.FormatDateRange(edu.StartDate, edu.EndDate, edu.Current) }</div>
		</div>
		<div class="institution-location">
			<span class="institution">{ edu.Institution }</span>
			if edu.Location != "" {
				<span class="location"> • { edu.Location }</span>
			}
		</div>
		if edu.GPA != "" {
			<p class="gpa">GPA: { edu.GPA }</p>
		}
		if edu.Description != "" {
			<p class="description">{ edu.Description }</p>
		}
	</div>
}

templ SkillsSection(skills []models.Skill) {
	if len(skills) > 0 {
		<section class="cv-section">
			<h2 class="section-title">Skills</h2>
			<div class="skills-container">
				for category, categorySkills := range groupSkillsByCategory(skills) {
					<div class="skill-category">
						<h4 class="category-title">{ category }</h4>
						<div class="skills-list">
							for _, skill := range categorySkills {
								<span class="skill-item" data-proficiency={ skill.Proficiency }>
									{ skill.Name }
								</span>
							}
						</div>
					</div>
				}
			</div>
		</section>
	}
}

templ ProjectsSection(projects []models.Project) {
	if len(projects) > 0 {
		<section class="cv-section">
			<h2 class="section-title">Projects</h2>
			for _, project := range projects {
				@ProjectItem(project)
			}
		</section>
	}
}

templ ProjectItem(project models.Project) {
	<div class="project-item">
		<div class="item-header">
			<h3 class="project-name">{ project.Name }</h3>
			<div class="date-range">{ models.FormatDateRange(project.StartDate, project.EndDate, project.Current) }</div>
		</div>
		<p class="description">{ project.Description }</p>
		if len(project.Technologies) > 0 {
			<div class="technologies">
				<strong>Technologies:</strong>
				for i, tech := range project.Technologies {
					if i > 0 {
						<span>, </span>
					}
					<span class="tech">{ tech }</span>
				}
			</div>
		}
		<div class="project-links">
			if project.URL != "" {
				<a href={ templ.URL(project.URL) } target="_blank" class="project-link">
					<i class="fas fa-external-link-alt"></i> Live Demo
				</a>
			}
			if project.GitHubURL != "" {
				<a href={ templ.URL(project.GitHubURL) } target="_blank" class="project-link">
					<i class="fab fa-github"></i> Source Code
				</a>
			}
		</div>
	</div>
}

templ CertificationsSection(certifications []models.Certification) {
	<section class="cv-section">
		<h2 class="section-title">Certifications</h2>
		for _, cert := range certifications {
			@CertificationItem(cert)
		}
	</section>
}

templ CertificationItem(cert models.Certification) {
	<div class="certification-item">
		<div class="item-header">
			<h3 class="cert-name">{ cert.Name }</h3>
			<div class="issue-date">{ models.FormatDate(cert.IssueDate) }</div>
		</div>
		<div class="issuer">{ cert.Issuer }</div>
		if cert.CredentialID != "" {
			<div class="credential-id">Credential ID: { cert.CredentialID }</div>
		}
		if cert.URL != "" {
			<a href={ templ.URL(cert.URL) } target="_blank" class="cert-link">
				<i class="fas fa-external-link-alt"></i> View Credential
			</a>
		}
	</div>
}

templ LanguagesSection(languages []models.Language) {
	<section class="cv-section">
		<h2 class="section-title">Languages</h2>
		<div class="languages-container">
			for _, lang := range languages {
				<div class="language-item">
					<span class="language-name">{ lang.Name }</span>
					<span class="language-proficiency">{ lang.Proficiency }</span>
				</div>
			}
		</div>
	</section>
}
