package templates

templ BaseLayout(title string) {
	<!DOCTYPE html>
	<html lang="en">
	<head>
		<meta charset="UTF-8"/>
		<meta name="viewport" content="width=device-width, initial-scale=1.0"/>
		<title>{ title } - CV Generator</title>
		<link rel="stylesheet" href="/static/css/style.css"/>
		<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet"/>
	</head>
	<body>
		<nav class="navbar">
			<div class="nav-container">
				<h1 class="nav-title">CV Generator</h1>
				<div class="nav-links">
					<a href="/" class="nav-link">Home</a>
					<a href="/form" class="nav-link">Create CV</a>
				</div>
			</div>
		</nav>
		<main class="main-content">
			{ children... }
		</main>
		<footer class="footer">
			<p>&copy; 2025 CV Generator. Built with Go and Templ.</p>
		</footer>
		<script src="/static/js/app.js"></script>
	</body>
	</html>
}

templ Home() {
	@BaseLayout("Home") {
		<div class="hero">
			<div class="hero-content">
				<h1>Professional CV Generator</h1>
				<p>Create a professional CV in minutes with our easy-to-use generator.</p>
				<div class="hero-buttons">
					<a href="/form" class="btn btn-primary">Create Your CV</a>
					<a href="#features" class="btn btn-secondary">Learn More</a>
				</div>
			</div>
		</div>
		
		<section id="features" class="features">
			<div class="container">
				<h2>Features</h2>
				<div class="features-grid">
					<div class="feature-card">
						<i class="fas fa-edit"></i>
						<h3>Easy to Use</h3>
						<p>Simple form-based interface to input your information</p>
					</div>
					<div class="feature-card">
						<i class="fas fa-eye"></i>
						<h3>Live Preview</h3>
						<p>See your CV update in real-time as you type</p>
					</div>
					<div class="feature-card">
						<i class="fas fa-download"></i>
						<h3>PDF Export</h3>
						<p>Download your CV as a professional PDF document</p>
					</div>
					<div class="feature-card">
						<i class="fas fa-mobile-alt"></i>
						<h3>Responsive</h3>
						<p>Works perfectly on desktop, tablet, and mobile devices</p>
					</div>
				</div>
			</div>
		</section>
	}
}
