package templates

import "cv-generator/internal/models"

// CVWithTemplate renders the CV using the selected template
templ CVWithTemplate(cv models.CV) {
	switch cv.Template {
		case "modern":
			@CVModern(cv)
		case "classic":
			@CVClassic(cv)
		case "creative":
			@CVCreative(cv)
		case "minimal":
			@CVMinimal(cv)
		case "tech":
			@CVTech(cv)
		default:
			@CVModern(cv)
	}
}

// Modern Professional Template
templ CVModern(cv models.CV) {
	<div class="cv-container template-modern">
		<header class="cv-header modern-header">
			<div class="header-content">
				<div class="personal-info">
					<h1 class="name">{ cv.PersonalInfo.FullName }</h1>
					if cv.PersonalInfo.Summary != "" {
						<p class="summary">{ cv.PersonalInfo.Summary }</p>
					}
				</div>
				<div class="contact-info">
					if cv.PersonalInfo.Email != "" {
						<div class="contact-item">
							<i class="fas fa-envelope"></i>
							<span>{ cv.PersonalInfo.Email }</span>
						</div>
					}
					if cv.PersonalInfo.Phone != "" {
						<div class="contact-item">
							<i class="fas fa-phone"></i>
							<span>{ cv.PersonalInfo.Phone }</span>
						</div>
					}
					if cv.PersonalInfo.Address != "" {
						<div class="contact-item">
							<i class="fas fa-map-marker-alt"></i>
							<span>{ cv.PersonalInfo.Address }</span>
						</div>
					}
					if cv.PersonalInfo.LinkedIn != "" {
						<div class="contact-item">
							<i class="fab fa-linkedin"></i>
							<a href={ templ.URL(cv.PersonalInfo.LinkedIn) } target="_blank">LinkedIn</a>
						</div>
					}
					if cv.PersonalInfo.GitHub != "" {
						<div class="contact-item">
							<i class="fab fa-github"></i>
							<a href={ templ.URL(cv.PersonalInfo.GitHub) } target="_blank">GitHub</a>
						</div>
					}
				</div>
			</div>
		</header>

		<main class="cv-content">
			if len(cv.Experiences) > 0 {
				<section class="cv-section">
					<h2 class="section-title">
						<i class="fas fa-briefcase"></i>
						Professional Experience
					</h2>
					for _, exp := range cv.Experiences {
						<div class="experience-item">
							<div class="item-header">
								<h3 class="job-title">{ exp.JobTitle }</h3>
								<div class="date-range">{ models.FormatDateRange(exp.StartDate, exp.EndDate, exp.Current) }</div>
							</div>
							<div class="company-location">
								<span class="company">{ exp.Company }</span>
								if exp.Location != "" {
									<span class="location">{ exp.Location }</span>
								}
							</div>
							if exp.Description != "" {
								<p class="description">{ exp.Description }</p>
							}
							if len(exp.Achievements) > 0 {
								<ul class="achievements">
									for _, achievement := range exp.Achievements {
										<li>{ achievement }</li>
									}
								</ul>
							}
						</div>
					}
				</section>
			}

			if len(cv.Education) > 0 {
				<section class="cv-section">
					<h2 class="section-title">
						<i class="fas fa-graduation-cap"></i>
						Education
					</h2>
					for _, edu := range cv.Education {
						<div class="education-item">
							<div class="item-header">
								<h3 class="degree">{ edu.Degree }</h3>
								<div class="date-range">{ models.FormatDateRange(edu.StartDate, edu.EndDate, edu.Current) }</div>
							</div>
							<div class="institution-location">
								<span class="institution">{ edu.Institution }</span>
								if edu.Location != "" {
									<span class="location">{ edu.Location }</span>
								}
							</div>
							if edu.GPA != "" {
								<div class="gpa">GPA: { edu.GPA }</div>
							}
							if edu.Description != "" {
								<p class="description">{ edu.Description }</p>
							}
						</div>
					}
				</section>
			}

			if len(cv.Skills) > 0 {
				<section class="cv-section">
					<h2 class="section-title">
						<i class="fas fa-cogs"></i>
						Skills
					</h2>
					<div class="skills-grid">
						for _, skill := range cv.Skills {
							<div class="skill-item">
								<span class="skill-name">{ skill.Name }</span>
								if skill.Proficiency != "" {
									<span class="skill-level">{ skill.Proficiency }</span>
								}
							</div>
						}
					</div>
				</section>
			}

			if len(cv.Projects) > 0 {
				<section class="cv-section">
					<h2 class="section-title">
						<i class="fas fa-project-diagram"></i>
						Projects
					</h2>
					for _, project := range cv.Projects {
						<div class="project-item">
							<div class="item-header">
								<h3 class="project-name">{ project.Name }</h3>
								<div class="date-range">{ models.FormatDateRange(project.StartDate, project.EndDate, project.Current) }</div>
							</div>
							if project.Description != "" {
								<p class="description">{ project.Description }</p>
							}
							if len(project.Technologies) > 0 {
								<div class="technologies">
									<strong>Technologies:</strong>
									for i, tech := range project.Technologies {
										if i > 0 {
											<span>, </span>
										}
										<span class="tech">{ tech }</span>
									}
								</div>
							}
							<div class="project-links">
								if project.URL != "" {
									<a href={ templ.URL(project.URL) } target="_blank" class="project-link">
										<i class="fas fa-external-link-alt"></i> Live Demo
									</a>
								}
								if project.GitHubURL != "" {
									<a href={ templ.URL(project.GitHubURL) } target="_blank" class="project-link">
										<i class="fab fa-github"></i> Source Code
									</a>
								}
							</div>
						</div>
					}
				</section>
			}
		</main>
	</div>
}

// Classic Executive Template
templ CVClassic(cv models.CV) {
	<div class="cv-container template-classic">
		<!-- Classic template with traditional styling -->
		<header class="cv-header classic-header">
			<h1 class="name">{ cv.PersonalInfo.FullName }</h1>
			<div class="contact-line">
				if cv.PersonalInfo.Email != "" {
					<span>{ cv.PersonalInfo.Email }</span>
				}
				if cv.PersonalInfo.Phone != "" {
					<span>{ cv.PersonalInfo.Phone }</span>
				}
				if cv.PersonalInfo.Address != "" {
					<span>{ cv.PersonalInfo.Address }</span>
				}
			</div>
		</header>
		<!-- Rest of classic template content -->
		@CVModern(cv)
	</div>
}

// Creative Designer Template
templ CVCreative(cv models.CV) {
	<div class="cv-container template-creative">
		<!-- Creative template with bold colors -->
		@CVModern(cv)
	</div>
}

// Minimal Clean Template
templ CVMinimal(cv models.CV) {
	<div class="cv-container template-minimal">
		<!-- Minimal template with clean typography -->
		@CVModern(cv)
	</div>
}

// Tech Professional Template
templ CVTech(cv models.CV) {
	<div class="cv-container template-tech">
		<!-- Tech-focused template -->
		@CVModern(cv)
	</div>
}
