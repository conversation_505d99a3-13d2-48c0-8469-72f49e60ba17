package models

import "time"

// PersonalInfo represents the personal information section of a CV
type PersonalInfo struct {
	FullName    string `json:"full_name" form:"full_name"`
	Email       string `json:"email" form:"email"`
	Phone       string `json:"phone" form:"phone"`
	Address     string `json:"address" form:"address"`
	LinkedIn    string `json:"linkedin" form:"linkedin"`
	GitHub      string `json:"github" form:"github"`
	Website     string `json:"website" form:"website"`
	Summary     string `json:"summary" form:"summary"`
}

// Experience represents a work experience entry
type Experience struct {
	JobTitle    string    `json:"job_title" form:"job_title"`
	Company     string    `json:"company" form:"company"`
	Location    string    `json:"location" form:"location"`
	StartDate   time.Time `json:"start_date" form:"start_date"`
	EndDate     *time.Time `json:"end_date,omitempty" form:"end_date"`
	Current     bool      `json:"current" form:"current"`
	Description string    `json:"description" form:"description"`
	Achievements []string `json:"achievements" form:"achievements"`
}

// Education represents an education entry
type Education struct {
	Degree      string    `json:"degree" form:"degree"`
	Institution string    `json:"institution" form:"institution"`
	Location    string    `json:"location" form:"location"`
	StartDate   time.Time `json:"start_date" form:"start_date"`
	EndDate     *time.Time `json:"end_date,omitempty" form:"end_date"`
	Current     bool      `json:"current" form:"current"`
	GPA         string    `json:"gpa,omitempty" form:"gpa"`
	Description string    `json:"description,omitempty" form:"description"`
}

// Skill represents a skill with proficiency level
type Skill struct {
	Name        string `json:"name" form:"name"`
	Category    string `json:"category" form:"category"` // e.g., "Programming", "Languages", "Tools"
	Proficiency string `json:"proficiency" form:"proficiency"` // e.g., "Beginner", "Intermediate", "Advanced", "Expert"
}

// Project represents a project entry
type Project struct {
	Name        string   `json:"name" form:"name"`
	Description string   `json:"description" form:"description"`
	Technologies []string `json:"technologies" form:"technologies"`
	URL         string   `json:"url,omitempty" form:"url"`
	GitHubURL   string   `json:"github_url,omitempty" form:"github_url"`
	StartDate   time.Time `json:"start_date" form:"start_date"`
	EndDate     *time.Time `json:"end_date,omitempty" form:"end_date"`
	Current     bool     `json:"current" form:"current"`
}

// Certification represents a certification or award
type Certification struct {
	Name         string    `json:"name" form:"name"`
	Issuer       string    `json:"issuer" form:"issuer"`
	IssueDate    time.Time `json:"issue_date" form:"issue_date"`
	ExpiryDate   *time.Time `json:"expiry_date,omitempty" form:"expiry_date"`
	CredentialID string    `json:"credential_id,omitempty" form:"credential_id"`
	URL          string    `json:"url,omitempty" form:"url"`
}

// Language represents a language skill
type Language struct {
	Name        string `json:"name" form:"name"`
	Proficiency string `json:"proficiency" form:"proficiency"` // e.g., "Native", "Fluent", "Conversational", "Basic"
}

// CV represents the complete CV data structure
type CV struct {
	PersonalInfo    PersonalInfo     `json:"personal_info"`
	Experiences     []Experience     `json:"experiences"`
	Education       []Education      `json:"education"`
	Skills          []Skill          `json:"skills"`
	Projects        []Project        `json:"projects"`
	Certifications  []Certification  `json:"certifications"`
	Languages       []Language       `json:"languages"`
	CreatedAt       time.Time        `json:"created_at"`
	UpdatedAt       time.Time        `json:"updated_at"`
}

// NewCV creates a new CV with default values
func NewCV() *CV {
	now := time.Now()
	return &CV{
		PersonalInfo:   PersonalInfo{},
		Experiences:    []Experience{},
		Education:      []Education{},
		Skills:         []Skill{},
		Projects:       []Project{},
		Certifications: []Certification{},
		Languages:      []Language{},
		CreatedAt:      now,
		UpdatedAt:      now,
	}
}

// FormatDate formats a date for display
func FormatDate(date time.Time) string {
	return date.Format("January 2006")
}

// FormatDateRange formats a date range for display
func FormatDateRange(start time.Time, end *time.Time, current bool) string {
	startStr := FormatDate(start)
	if current {
		return startStr + " - Present"
	}
	if end != nil {
		return startStr + " - " + FormatDate(*end)
	}
	return startStr
}
