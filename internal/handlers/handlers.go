package handlers

import (
	"bytes"
	"fmt"
	"log"
	"net/http"
	"strconv"
	"strings"
	"time"

	"cv-generator/internal/models"
	"cv-generator/internal/services"
	"cv-generator/internal/templates"
)

type Handlers struct {
	pdfService *services.PDFService
}

func New() *Handlers {
	return &Handlers{
		pdfService: services.NewPDFService(),
	}
}

// Home renders the home page
func (h *Handlers) Home(w http.ResponseWriter, r *http.Request) {
	if r.URL.Path != "/" {
		http.NotFound(w, r)
		return
	}

	component := templates.Home()
	err := component.Render(r.Context(), w)
	if err != nil {
		log.Printf("Error rendering home template: %v", err)
		http.Error(w, "Internal Server Error", http.StatusInternalServerError)
		return
	}
}

// Form renders the CV form page
func (h *Handlers) Form(w http.ResponseWriter, r *http.Request) {
	component := templates.SimpleForm()
	err := component.Render(r.Context(), w)
	if err != nil {
		log.Printf("Error rendering form template: %v", err)
		http.Error(w, "Internal Server Error", http.StatusInternalServerError)
		return
	}
}

// Preview handles CV preview requests
func (h *Handlers) Preview(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	cv, err := h.parseFormData(r)
	if err != nil {
		log.Printf("Error parsing form data: %v", err)
		http.Error(w, "Bad Request", http.StatusBadRequest)
		return
	}

	// Return just the CV content for AJAX preview
	component := templates.CVComplete(*cv)
	err = component.Render(r.Context(), w)
	if err != nil {
		log.Printf("Error rendering CV preview: %v", err)
		http.Error(w, "Internal Server Error", http.StatusInternalServerError)
		return
	}
}

// Generate handles CV generation and PDF export
func (h *Handlers) Generate(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	cv, err := h.parseFormData(r)
	if err != nil {
		log.Printf("Error parsing form data: %v", err)
		http.Error(w, "Bad Request", http.StatusBadRequest)
		return
	}

	// Check if PDF is requested
	if r.FormValue("format") == "pdf" {
		// Generate HTML first
		var htmlBuffer bytes.Buffer
		component := templates.CVComplete(*cv)
		err = component.Render(r.Context(), &htmlBuffer)
		if err != nil {
			log.Printf("Error rendering CV HTML: %v", err)
			http.Error(w, "Internal Server Error", http.StatusInternalServerError)
			return
		}

		// Try to generate PDF from HTML
		pdfData, err := h.pdfService.GeneratePDFWithCustomCSS(htmlBuffer.String())
		if err != nil {
			log.Printf("Error generating PDF: %v", err)

			// Fallback: Return HTML with print-friendly styling and instructions
			w.Header().Set("Content-Type", "text/html; charset=utf-8")
			w.Header().Set("Content-Disposition", "inline; filename=cv.html")

			// Add print instructions to the HTML
			printInstructions := `
				<div id="print-instructions" style="background: #f0f9ff; border: 2px solid #0ea5e9; border-radius: 8px; padding: 20px; margin: 20px; text-align: center; font-family: Arial, sans-serif;">
					<h2 style="color: #0369a1; margin-bottom: 10px;">📄 PDF Generation Not Available</h2>
					<p style="color: #0c4a6e; margin-bottom: 15px;">Chrome/Chromium is not installed on this server. To get a PDF version of your CV:</p>
					<ol style="color: #0c4a6e; text-align: left; display: inline-block; margin-bottom: 15px;">
						<li>Press <strong>Ctrl+P</strong> (or Cmd+P on Mac)</li>
						<li>Select "Save as PDF" as the destination</li>
						<li>Choose "More settings" and set margins to "Minimum"</li>
						<li>Enable "Background graphics" for best results</li>
						<li>Click "Save"</li>
					</ol>
					<button onclick="window.print(); document.getElementById('print-instructions').style.display='none';"
						style="background: #0ea5e9; color: white; border: none; padding: 12px 24px; border-radius: 6px; font-size: 16px; cursor: pointer; margin-top: 10px;">
						🖨️ Print/Save as PDF
					</button>
				</div>
				<style>
					@media print {
						#print-instructions { display: none !important; }
					}
				</style>
			`

			// Insert instructions at the beginning of the HTML
			htmlContent := htmlBuffer.String()
			if bodyIndex := strings.Index(htmlContent, "<body"); bodyIndex != -1 {
				if bodyEndIndex := strings.Index(htmlContent[bodyIndex:], ">"); bodyEndIndex != -1 {
					insertPoint := bodyIndex + bodyEndIndex + 1
					htmlContent = htmlContent[:insertPoint] + printInstructions + htmlContent[insertPoint:]
				}
			}

			_, err = w.Write([]byte(htmlContent))
			if err != nil {
				log.Printf("Error writing HTML fallback: %v", err)
			}
			return
		}

		// Set headers for PDF download
		w.Header().Set("Content-Type", "application/pdf")
		w.Header().Set("Content-Disposition", "attachment; filename=cv.pdf")
		w.Header().Set("Content-Length", fmt.Sprintf("%d", len(pdfData)))

		// Write PDF data
		_, err = w.Write(pdfData)
		if err != nil {
			log.Printf("Error writing PDF data: %v", err)
		}
	} else {
		// Return HTML
		w.Header().Set("Content-Type", "text/html")
		w.Header().Set("Content-Disposition", "attachment; filename=cv.html")

		component := templates.CVComplete(*cv)
		err = component.Render(r.Context(), w)
		if err != nil {
			log.Printf("Error rendering CV: %v", err)
			http.Error(w, "Internal Server Error", http.StatusInternalServerError)
			return
		}
	}
}

// parseFormData parses the form data and returns a CV model
func (h *Handlers) parseFormData(r *http.Request) (*models.CV, error) {
	err := r.ParseForm()
	if err != nil {
		return nil, fmt.Errorf("failed to parse form: %w", err)
	}

	cv := models.NewCV()

	// Parse personal info
	cv.PersonalInfo = models.PersonalInfo{
		FullName: r.FormValue("full_name"),
		Email:    r.FormValue("email"),
		Phone:    r.FormValue("phone"),
		Address:  r.FormValue("address"),
		LinkedIn: r.FormValue("linkedin"),
		GitHub:   r.FormValue("github"),
		Website:  r.FormValue("website"),
		Summary:  r.FormValue("summary"),
	}

	// Parse simple experience
	if jobTitle := r.FormValue("job_title"); jobTitle != "" {
		exp := models.Experience{
			JobTitle:    jobTitle,
			Company:     r.FormValue("company"),
			Location:    r.FormValue("job_location"),
			Current:     r.FormValue("current_job") == "on",
			Description: r.FormValue("job_description"),
		}

		// Parse start date
		if startDateStr := r.FormValue("start_date"); startDateStr != "" {
			if startDate, err := time.Parse("2006-01", startDateStr); err == nil {
				exp.StartDate = startDate
			}
		}

		// Parse end date
		if endDateStr := r.FormValue("end_date"); endDateStr != "" && !exp.Current {
			if endDate, err := time.Parse("2006-01", endDateStr); err == nil {
				exp.EndDate = &endDate
			}
		}

		cv.Experiences = []models.Experience{exp}
	}

	// Parse simple education
	if degree := r.FormValue("degree"); degree != "" {
		edu := models.Education{
			Degree:      degree,
			Institution: r.FormValue("institution"),
			Location:    r.FormValue("edu_location"),
			GPA:         r.FormValue("gpa"),
		}

		// Parse start date
		if startDateStr := r.FormValue("edu_start_date"); startDateStr != "" {
			if startDate, err := time.Parse("2006-01", startDateStr); err == nil {
				edu.StartDate = startDate
			}
		}

		// Parse end date
		if endDateStr := r.FormValue("edu_end_date"); endDateStr != "" {
			if endDate, err := time.Parse("2006-01", endDateStr); err == nil {
				edu.EndDate = &endDate
			}
		}

		cv.Education = []models.Education{edu}
	}

	// Parse simple skills
	if skillsStr := r.FormValue("skills"); skillsStr != "" {
		skillNames := strings.Split(skillsStr, ",")
		for _, skillName := range skillNames {
			skillName = strings.TrimSpace(skillName)
			if skillName != "" {
				cv.Skills = append(cv.Skills, models.Skill{
					Name:        skillName,
					Category:    "Technical",
					Proficiency: "Intermediate",
				})
			}
		}
	}

	// Parse simple project
	if projectName := r.FormValue("project_name"); projectName != "" {
		project := models.Project{
			Name:        projectName,
			Description: r.FormValue("project_description"),
			URL:         r.FormValue("project_url"),
			StartDate:   time.Now().AddDate(-1, 0, 0), // Default to 1 year ago
		}

		// Parse technologies
		if techStr := r.FormValue("project_technologies"); techStr != "" {
			techs := strings.Split(techStr, ",")
			for _, tech := range techs {
				tech = strings.TrimSpace(tech)
				if tech != "" {
					project.Technologies = append(project.Technologies, tech)
				}
			}
		}

		cv.Projects = []models.Project{project}
	}

	cv.UpdatedAt = time.Now()

	return cv, nil
}

// parseExperiences parses experience entries from form data
func (h *Handlers) parseExperiences(r *http.Request) []models.Experience {
	var experiences []models.Experience

	// Find all experience indices
	indices := h.findFormIndices(r, "experiences", "job_title")

	for _, index := range indices {
		exp := models.Experience{
			JobTitle:    r.FormValue(fmt.Sprintf("experiences[%d].job_title", index)),
			Company:     r.FormValue(fmt.Sprintf("experiences[%d].company", index)),
			Location:    r.FormValue(fmt.Sprintf("experiences[%d].location", index)),
			Current:     r.FormValue(fmt.Sprintf("experiences[%d].current", index)) == "on",
			Description: r.FormValue(fmt.Sprintf("experiences[%d].description", index)),
		}

		// Parse start date
		if startDateStr := r.FormValue(fmt.Sprintf("experiences[%d].start_date", index)); startDateStr != "" {
			if startDate, err := time.Parse("2006-01", startDateStr); err == nil {
				exp.StartDate = startDate
			}
		}

		// Parse end date
		if endDateStr := r.FormValue(fmt.Sprintf("experiences[%d].end_date", index)); endDateStr != "" && !exp.Current {
			if endDate, err := time.Parse("2006-01", endDateStr); err == nil {
				exp.EndDate = &endDate
			}
		}

		// Parse achievements
		if achievementsStr := r.FormValue(fmt.Sprintf("experiences[%d].achievements", index)); achievementsStr != "" {
			lines := strings.Split(achievementsStr, "\n")
			for _, line := range lines {
				line = strings.TrimSpace(line)
				if line != "" {
					exp.Achievements = append(exp.Achievements, line)
				}
			}
		}

		if exp.JobTitle != "" && exp.Company != "" {
			experiences = append(experiences, exp)
		}
	}

	return experiences
}

// parseEducation parses education entries from form data
func (h *Handlers) parseEducation(r *http.Request) []models.Education {
	var education []models.Education

	indices := h.findFormIndices(r, "education", "degree")

	for _, index := range indices {
		edu := models.Education{
			Degree:      r.FormValue(fmt.Sprintf("education[%d].degree", index)),
			Institution: r.FormValue(fmt.Sprintf("education[%d].institution", index)),
			Location:    r.FormValue(fmt.Sprintf("education[%d].location", index)),
			Current:     r.FormValue(fmt.Sprintf("education[%d].current", index)) == "on",
			GPA:         r.FormValue(fmt.Sprintf("education[%d].gpa", index)),
			Description: r.FormValue(fmt.Sprintf("education[%d].description", index)),
		}

		// Parse start date
		if startDateStr := r.FormValue(fmt.Sprintf("education[%d].start_date", index)); startDateStr != "" {
			if startDate, err := time.Parse("2006-01", startDateStr); err == nil {
				edu.StartDate = startDate
			}
		}

		// Parse end date
		if endDateStr := r.FormValue(fmt.Sprintf("education[%d].end_date", index)); endDateStr != "" && !edu.Current {
			if endDate, err := time.Parse("2006-01", endDateStr); err == nil {
				edu.EndDate = &endDate
			}
		}

		if edu.Degree != "" && edu.Institution != "" {
			education = append(education, edu)
		}
	}

	return education
}

// parseSkills parses skill entries from form data
func (h *Handlers) parseSkills(r *http.Request) []models.Skill {
	var skills []models.Skill

	indices := h.findFormIndices(r, "skills", "name")

	for _, index := range indices {
		skill := models.Skill{
			Name:        r.FormValue(fmt.Sprintf("skills[%d].name", index)),
			Category:    r.FormValue(fmt.Sprintf("skills[%d].category", index)),
			Proficiency: r.FormValue(fmt.Sprintf("skills[%d].proficiency", index)),
		}

		if skill.Name != "" {
			skills = append(skills, skill)
		}
	}

	return skills
}

// parseProjects parses project entries from form data
func (h *Handlers) parseProjects(r *http.Request) []models.Project {
	var projects []models.Project

	indices := h.findFormIndices(r, "projects", "name")

	for _, index := range indices {
		project := models.Project{
			Name:        r.FormValue(fmt.Sprintf("projects[%d].name", index)),
			Description: r.FormValue(fmt.Sprintf("projects[%d].description", index)),
			URL:         r.FormValue(fmt.Sprintf("projects[%d].url", index)),
			GitHubURL:   r.FormValue(fmt.Sprintf("projects[%d].github_url", index)),
			Current:     r.FormValue(fmt.Sprintf("projects[%d].current", index)) == "on",
		}

		// Parse start date
		if startDateStr := r.FormValue(fmt.Sprintf("projects[%d].start_date", index)); startDateStr != "" {
			if startDate, err := time.Parse("2006-01", startDateStr); err == nil {
				project.StartDate = startDate
			}
		}

		// Parse end date
		if endDateStr := r.FormValue(fmt.Sprintf("projects[%d].end_date", index)); endDateStr != "" && !project.Current {
			if endDate, err := time.Parse("2006-01", endDateStr); err == nil {
				project.EndDate = &endDate
			}
		}

		// Parse technologies
		if techStr := r.FormValue(fmt.Sprintf("projects[%d].technologies", index)); techStr != "" {
			techs := strings.Split(techStr, ",")
			for _, tech := range techs {
				tech = strings.TrimSpace(tech)
				if tech != "" {
					project.Technologies = append(project.Technologies, tech)
				}
			}
		}

		if project.Name != "" && project.Description != "" {
			projects = append(projects, project)
		}
	}

	return projects
}

// parseCertifications parses certification entries from form data
func (h *Handlers) parseCertifications(r *http.Request) []models.Certification {
	var certifications []models.Certification

	indices := h.findFormIndices(r, "certifications", "name")

	for _, index := range indices {
		cert := models.Certification{
			Name:         r.FormValue(fmt.Sprintf("certifications[%d].name", index)),
			Issuer:       r.FormValue(fmt.Sprintf("certifications[%d].issuer", index)),
			CredentialID: r.FormValue(fmt.Sprintf("certifications[%d].credential_id", index)),
			URL:          r.FormValue(fmt.Sprintf("certifications[%d].url", index)),
		}

		// Parse issue date
		if issueDateStr := r.FormValue(fmt.Sprintf("certifications[%d].issue_date", index)); issueDateStr != "" {
			if issueDate, err := time.Parse("2006-01", issueDateStr); err == nil {
				cert.IssueDate = issueDate
			}
		}

		// Parse expiry date
		if expiryDateStr := r.FormValue(fmt.Sprintf("certifications[%d].expiry_date", index)); expiryDateStr != "" {
			if expiryDate, err := time.Parse("2006-01", expiryDateStr); err == nil {
				cert.ExpiryDate = &expiryDate
			}
		}

		if cert.Name != "" && cert.Issuer != "" {
			certifications = append(certifications, cert)
		}
	}

	return certifications
}

// parseLanguages parses language entries from form data
func (h *Handlers) parseLanguages(r *http.Request) []models.Language {
	var languages []models.Language

	indices := h.findFormIndices(r, "languages", "name")

	for _, index := range indices {
		lang := models.Language{
			Name:        r.FormValue(fmt.Sprintf("languages[%d].name", index)),
			Proficiency: r.FormValue(fmt.Sprintf("languages[%d].proficiency", index)),
		}

		if lang.Name != "" {
			languages = append(languages, lang)
		}
	}

	return languages
}

// findFormIndices finds all indices for a given form field prefix
func (h *Handlers) findFormIndices(r *http.Request, section, field string) []int {
	var indices []int
	indexMap := make(map[int]bool)

	prefix := fmt.Sprintf("%s[", section)
	suffix := fmt.Sprintf("].%s", field)

	for key := range r.Form {
		if strings.HasPrefix(key, prefix) && strings.HasSuffix(key, suffix) {
			// Extract index from key like "experiences[0].job_title"
			indexStr := strings.TrimPrefix(key, prefix)
			indexStr = strings.TrimSuffix(indexStr, suffix)

			if index, err := strconv.Atoi(indexStr); err == nil {
				indexMap[index] = true
			}
		}
	}

	// Convert map to sorted slice
	for index := range indexMap {
		indices = append(indices, index)
	}

	return indices
}
