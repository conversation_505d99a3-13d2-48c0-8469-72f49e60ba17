package handlers

import (
	"fmt"
	"log"
	"net/http"
	"strconv"
	"strings"
	"time"

	"cv-generator/internal/models"
	"cv-generator/internal/templates"
)

type Handlers struct {
	// Add any dependencies here (e.g., database, services)
}

func New() *Handlers {
	return &Handlers{}
}

// Home renders the home page
func (h *Handlers) Home(w http.ResponseWriter, r *http.Request) {
	if r.URL.Path != "/" {
		http.NotFound(w, r)
		return
	}

	component := templates.Home()
	err := component.Render(r.Context(), w)
	if err != nil {
		log.Printf("Error rendering home template: %v", err)
		http.Error(w, "Internal Server Error", http.StatusInternalServerError)
		return
	}
}

// Form renders the CV form page
func (h *Handlers) Form(w http.ResponseWriter, r *http.Request) {
	cv := models.NewCV()

	component := templates.CVForm(*cv)
	err := component.Render(r.Context(), w)
	if err != nil {
		log.Printf("Error rendering form template: %v", err)
		http.Error(w, "Internal Server Error", http.StatusInternalServerError)
		return
	}
}

// Preview handles CV preview requests
func (h *Handlers) Preview(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	cv, err := h.parseFormData(r)
	if err != nil {
		log.Printf("Error parsing form data: %v", err)
		http.Error(w, "Bad Request", http.StatusBadRequest)
		return
	}

	// Return just the CV content for AJAX preview
	component := templates.CVComplete(*cv)
	err = component.Render(r.Context(), w)
	if err != nil {
		log.Printf("Error rendering CV preview: %v", err)
		http.Error(w, "Internal Server Error", http.StatusInternalServerError)
		return
	}
}

// Generate handles CV generation and PDF export
func (h *Handlers) Generate(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	cv, err := h.parseFormData(r)
	if err != nil {
		log.Printf("Error parsing form data: %v", err)
		http.Error(w, "Bad Request", http.StatusBadRequest)
		return
	}

	// For now, return HTML. PDF generation will be added later
	w.Header().Set("Content-Type", "text/html")
	w.Header().Set("Content-Disposition", "attachment; filename=cv.html")

	component := templates.CVComplete(*cv)
	err = component.Render(r.Context(), w)
	if err != nil {
		log.Printf("Error rendering CV: %v", err)
		http.Error(w, "Internal Server Error", http.StatusInternalServerError)
		return
	}
}

// parseFormData parses the form data and returns a CV model
func (h *Handlers) parseFormData(r *http.Request) (*models.CV, error) {
	err := r.ParseForm()
	if err != nil {
		return nil, fmt.Errorf("failed to parse form: %w", err)
	}

	cv := models.NewCV()

	// Parse personal info
	cv.PersonalInfo = models.PersonalInfo{
		FullName: r.FormValue("full_name"),
		Email:    r.FormValue("email"),
		Phone:    r.FormValue("phone"),
		Address:  r.FormValue("address"),
		LinkedIn: r.FormValue("linkedin"),
		GitHub:   r.FormValue("github"),
		Website:  r.FormValue("website"),
		Summary:  r.FormValue("summary"),
	}

	// Parse experiences
	cv.Experiences = h.parseExperiences(r)

	// Parse education
	cv.Education = h.parseEducation(r)

	// Parse skills
	cv.Skills = h.parseSkills(r)

	// Parse projects
	cv.Projects = h.parseProjects(r)

	// Parse certifications
	cv.Certifications = h.parseCertifications(r)

	// Parse languages
	cv.Languages = h.parseLanguages(r)

	cv.UpdatedAt = time.Now()

	return cv, nil
}

// parseExperiences parses experience entries from form data
func (h *Handlers) parseExperiences(r *http.Request) []models.Experience {
	var experiences []models.Experience

	// Find all experience indices
	indices := h.findFormIndices(r, "experiences", "job_title")

	for _, index := range indices {
		exp := models.Experience{
			JobTitle:    r.FormValue(fmt.Sprintf("experiences[%d].job_title", index)),
			Company:     r.FormValue(fmt.Sprintf("experiences[%d].company", index)),
			Location:    r.FormValue(fmt.Sprintf("experiences[%d].location", index)),
			Current:     r.FormValue(fmt.Sprintf("experiences[%d].current", index)) == "on",
			Description: r.FormValue(fmt.Sprintf("experiences[%d].description", index)),
		}

		// Parse start date
		if startDateStr := r.FormValue(fmt.Sprintf("experiences[%d].start_date", index)); startDateStr != "" {
			if startDate, err := time.Parse("2006-01", startDateStr); err == nil {
				exp.StartDate = startDate
			}
		}

		// Parse end date
		if endDateStr := r.FormValue(fmt.Sprintf("experiences[%d].end_date", index)); endDateStr != "" && !exp.Current {
			if endDate, err := time.Parse("2006-01", endDateStr); err == nil {
				exp.EndDate = &endDate
			}
		}

		// Parse achievements
		if achievementsStr := r.FormValue(fmt.Sprintf("experiences[%d].achievements", index)); achievementsStr != "" {
			lines := strings.Split(achievementsStr, "\n")
			for _, line := range lines {
				line = strings.TrimSpace(line)
				if line != "" {
					exp.Achievements = append(exp.Achievements, line)
				}
			}
		}

		if exp.JobTitle != "" && exp.Company != "" {
			experiences = append(experiences, exp)
		}
	}

	return experiences
}

// parseEducation parses education entries from form data
func (h *Handlers) parseEducation(r *http.Request) []models.Education {
	var education []models.Education

	indices := h.findFormIndices(r, "education", "degree")

	for _, index := range indices {
		edu := models.Education{
			Degree:      r.FormValue(fmt.Sprintf("education[%d].degree", index)),
			Institution: r.FormValue(fmt.Sprintf("education[%d].institution", index)),
			Location:    r.FormValue(fmt.Sprintf("education[%d].location", index)),
			Current:     r.FormValue(fmt.Sprintf("education[%d].current", index)) == "on",
			GPA:         r.FormValue(fmt.Sprintf("education[%d].gpa", index)),
			Description: r.FormValue(fmt.Sprintf("education[%d].description", index)),
		}

		// Parse start date
		if startDateStr := r.FormValue(fmt.Sprintf("education[%d].start_date", index)); startDateStr != "" {
			if startDate, err := time.Parse("2006-01", startDateStr); err == nil {
				edu.StartDate = startDate
			}
		}

		// Parse end date
		if endDateStr := r.FormValue(fmt.Sprintf("education[%d].end_date", index)); endDateStr != "" && !edu.Current {
			if endDate, err := time.Parse("2006-01", endDateStr); err == nil {
				edu.EndDate = &endDate
			}
		}

		if edu.Degree != "" && edu.Institution != "" {
			education = append(education, edu)
		}
	}

	return education
}

// parseSkills parses skill entries from form data
func (h *Handlers) parseSkills(r *http.Request) []models.Skill {
	var skills []models.Skill

	indices := h.findFormIndices(r, "skills", "name")

	for _, index := range indices {
		skill := models.Skill{
			Name:        r.FormValue(fmt.Sprintf("skills[%d].name", index)),
			Category:    r.FormValue(fmt.Sprintf("skills[%d].category", index)),
			Proficiency: r.FormValue(fmt.Sprintf("skills[%d].proficiency", index)),
		}

		if skill.Name != "" {
			skills = append(skills, skill)
		}
	}

	return skills
}

// parseProjects parses project entries from form data
func (h *Handlers) parseProjects(r *http.Request) []models.Project {
	var projects []models.Project

	indices := h.findFormIndices(r, "projects", "name")

	for _, index := range indices {
		project := models.Project{
			Name:        r.FormValue(fmt.Sprintf("projects[%d].name", index)),
			Description: r.FormValue(fmt.Sprintf("projects[%d].description", index)),
			URL:         r.FormValue(fmt.Sprintf("projects[%d].url", index)),
			GitHubURL:   r.FormValue(fmt.Sprintf("projects[%d].github_url", index)),
			Current:     r.FormValue(fmt.Sprintf("projects[%d].current", index)) == "on",
		}

		// Parse start date
		if startDateStr := r.FormValue(fmt.Sprintf("projects[%d].start_date", index)); startDateStr != "" {
			if startDate, err := time.Parse("2006-01", startDateStr); err == nil {
				project.StartDate = startDate
			}
		}

		// Parse end date
		if endDateStr := r.FormValue(fmt.Sprintf("projects[%d].end_date", index)); endDateStr != "" && !project.Current {
			if endDate, err := time.Parse("2006-01", endDateStr); err == nil {
				project.EndDate = &endDate
			}
		}

		// Parse technologies
		if techStr := r.FormValue(fmt.Sprintf("projects[%d].technologies", index)); techStr != "" {
			techs := strings.Split(techStr, ",")
			for _, tech := range techs {
				tech = strings.TrimSpace(tech)
				if tech != "" {
					project.Technologies = append(project.Technologies, tech)
				}
			}
		}

		if project.Name != "" && project.Description != "" {
			projects = append(projects, project)
		}
	}

	return projects
}

// parseCertifications parses certification entries from form data
func (h *Handlers) parseCertifications(r *http.Request) []models.Certification {
	var certifications []models.Certification

	indices := h.findFormIndices(r, "certifications", "name")

	for _, index := range indices {
		cert := models.Certification{
			Name:         r.FormValue(fmt.Sprintf("certifications[%d].name", index)),
			Issuer:       r.FormValue(fmt.Sprintf("certifications[%d].issuer", index)),
			CredentialID: r.FormValue(fmt.Sprintf("certifications[%d].credential_id", index)),
			URL:          r.FormValue(fmt.Sprintf("certifications[%d].url", index)),
		}

		// Parse issue date
		if issueDateStr := r.FormValue(fmt.Sprintf("certifications[%d].issue_date", index)); issueDateStr != "" {
			if issueDate, err := time.Parse("2006-01", issueDateStr); err == nil {
				cert.IssueDate = issueDate
			}
		}

		// Parse expiry date
		if expiryDateStr := r.FormValue(fmt.Sprintf("certifications[%d].expiry_date", index)); expiryDateStr != "" {
			if expiryDate, err := time.Parse("2006-01", expiryDateStr); err == nil {
				cert.ExpiryDate = &expiryDate
			}
		}

		if cert.Name != "" && cert.Issuer != "" {
			certifications = append(certifications, cert)
		}
	}

	return certifications
}

// parseLanguages parses language entries from form data
func (h *Handlers) parseLanguages(r *http.Request) []models.Language {
	var languages []models.Language

	indices := h.findFormIndices(r, "languages", "name")

	for _, index := range indices {
		lang := models.Language{
			Name:        r.FormValue(fmt.Sprintf("languages[%d].name", index)),
			Proficiency: r.FormValue(fmt.Sprintf("languages[%d].proficiency", index)),
		}

		if lang.Name != "" {
			languages = append(languages, lang)
		}
	}

	return languages
}

// findFormIndices finds all indices for a given form field prefix
func (h *Handlers) findFormIndices(r *http.Request, section, field string) []int {
	var indices []int
	indexMap := make(map[int]bool)

	prefix := fmt.Sprintf("%s[", section)
	suffix := fmt.Sprintf("].%s", field)

	for key := range r.Form {
		if strings.HasPrefix(key, prefix) && strings.HasSuffix(key, suffix) {
			// Extract index from key like "experiences[0].job_title"
			indexStr := strings.TrimPrefix(key, prefix)
			indexStr = strings.TrimSuffix(indexStr, suffix)

			if index, err := strconv.Atoi(indexStr); err == nil {
				indexMap[index] = true
			}
		}
	}

	// Convert map to sorted slice
	for index := range indexMap {
		indices = append(indices, index)
	}

	return indices
}
