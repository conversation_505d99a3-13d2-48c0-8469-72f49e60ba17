package services

import (
	"context"
	"fmt"
	"time"

	"github.com/chromedp/chromedp"
)

type PDFService struct {
	// Add any configuration here
}

func NewPDFService() *PDFService {
	return &PDFService{}
}

// GeneratePDFFromHTML converts HTML content to PDF
func (p *PDFService) GeneratePDFFromHTML(html string) ([]byte, error) {
	// Create context with timeout
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Create a new browser context
	ctx, cancel = chromedp.NewContext(ctx)
	defer cancel()

	var pdfBuffer []byte

	// Run chromedp tasks
	err := chromedp.Run(ctx,
		// Set the HTML content
		chromedp.Navigate("data:text/html,"+html),

		// Wait for the page to load
		chromedp.WaitReady("body"),

		// Generate PDF
		chromedp.ActionFunc(func(ctx context.Context) error {
			var err error
			pdfBuffer, _, err = chromedp.PrintToPDF().
				WithPrintBackground(true).
				WithPaperWidth(8.27).  // A4 width in inches
				WithPaperHeight(11.7). // A4 height in inches
				WithMarginTop(0.4).
				WithMarginBottom(0.4).
				WithMarginLeft(0.4).
				WithMarginRight(0.4).
				WithScale(0.8).
				Do(ctx)
			return err
		}),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to generate PDF: %w", err)
	}

	return pdfBuffer, nil
}

// GeneratePDFFromURL converts a URL to PDF
func (p *PDFService) GeneratePDFFromURL(url string) ([]byte, error) {
	// Create context with timeout
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Create a new browser context
	ctx, cancel = chromedp.NewContext(ctx)
	defer cancel()

	var pdfBuffer []byte

	// Run chromedp tasks
	err := chromedp.Run(ctx,
		// Navigate to the URL
		chromedp.Navigate(url),

		// Wait for the page to load
		chromedp.WaitReady("body"),

		// Wait a bit more for any dynamic content
		chromedp.Sleep(2*time.Second),

		// Generate PDF
		chromedp.ActionFunc(func(ctx context.Context) error {
			var err error
			pdfBuffer, _, err = chromedp.PrintToPDF().
				WithPrintBackground(true).
				WithPaperWidth(8.27).  // A4 width in inches
				WithPaperHeight(11.7). // A4 height in inches
				WithMarginTop(0.4).
				WithMarginBottom(0.4).
				WithMarginLeft(0.4).
				WithMarginRight(0.4).
				WithScale(0.8).
				Do(ctx)
			return err
		}),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to generate PDF from URL: %w", err)
	}

	return pdfBuffer, nil
}

// GeneratePDFWithCustomCSS generates PDF with custom CSS for print optimization
func (p *PDFService) GeneratePDFWithCustomCSS(html string) ([]byte, error) {
	// Add print-optimized CSS
	printCSS := `
		<style>
			@media print {
				* {
					-webkit-print-color-adjust: exact !important;
					color-adjust: exact !important;
				}
				
				body {
					margin: 0;
					padding: 0;
					font-family: 'Georgia', serif;
					font-size: 12px;
					line-height: 1.4;
					color: #333;
				}
				
				.cv-container {
					max-width: none;
					margin: 0;
					padding: 20px;
					box-shadow: none;
					background: white;
				}
				
				.cv-header {
					margin-bottom: 20px;
					padding-bottom: 15px;
					border-bottom: 2px solid #333;
				}
				
				.cv-header .name {
					font-size: 24px;
					margin-bottom: 5px;
				}
				
				.cv-header .summary {
					font-size: 11px;
					margin-bottom: 10px;
				}
				
				.contact-info {
					font-size: 10px;
					gap: 15px;
				}
				
				.section-title {
					font-size: 14px;
					margin-bottom: 10px;
					padding-bottom: 5px;
					border-bottom: 1px solid #ddd;
				}
				
				.experience-item,
				.education-item,
				.project-item,
				.certification-item {
					margin-bottom: 15px;
					padding-bottom: 10px;
					border-bottom: 1px solid #eee;
					page-break-inside: avoid;
				}
				
				.job-title,
				.degree,
				.project-name,
				.cert-name {
					font-size: 12px;
				}
				
				.date-range,
				.issue-date {
					font-size: 10px;
				}
				
				.description,
				.achievements li {
					font-size: 11px;
				}
				
				.skills-container {
					grid-template-columns: repeat(2, 1fr);
				}
				
				.skill-item {
					font-size: 9px;
					padding: 2px 6px;
				}
				
				.languages-container {
					grid-template-columns: repeat(3, 1fr);
				}
				
				.cv-section {
					page-break-inside: avoid;
					margin-bottom: 20px;
				}
			}
		</style>
	`

	// Insert the CSS into the HTML
	if len(html) > 0 {
		// Find the head tag and insert CSS
		headIndex := fmt.Sprintf("%s</head>", printCSS)
		html = fmt.Sprintf("%s%s", html[:len(html)-7], headIndex)
	}

	return p.GeneratePDFFromHTML(html)
}

// StartHeadlessBrowser starts a headless browser context for reuse
func (p *PDFService) StartHeadlessBrowser() (context.Context, context.CancelFunc, error) {
	// Create allocator context
	allocCtx, allocCancel := chromedp.NewExecAllocator(context.Background(),
		chromedp.NoSandbox,
		chromedp.Headless,
		chromedp.DisableGPU,
		chromedp.NoDefaultBrowserCheck,
		chromedp.Flag("disable-background-timer-throttling", true),
		chromedp.Flag("disable-backgrounding-occluded-windows", true),
		chromedp.Flag("disable-renderer-backgrounding", true),
	)

	// Create browser context
	ctx, cancel := chromedp.NewContext(allocCtx)

	// Start the browser
	err := chromedp.Run(ctx)
	if err != nil {
		cancel()
		allocCancel()
		return nil, nil, fmt.Errorf("failed to start browser: %w", err)
	}

	// Return a combined cancel function
	combinedCancel := func() {
		cancel()
		allocCancel()
	}

	return ctx, combinedCancel, nil
}
