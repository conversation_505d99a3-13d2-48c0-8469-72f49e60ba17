package main

import (
	"log"
	"net/http"

	"cv-generator/internal/handlers"
)

func main() {
	// Initialize handlers
	h := handlers.New()

	// Setup routes
	http.HandleFunc("/", h.Home)
	http.HandleFunc("/form", h.Form)
	http.HandleFunc("/preview", h.Preview)
	http.HandleFunc("/generate", h.Generate)
	
	// Serve static files
	http.Handle("/static/", http.StripPrefix("/static/", http.FileServer(http.Dir("./static/"))))

	log.Println("Server starting on :8080")
	log.Fatal(http.ListenAndServe(":8080", nil))
}
