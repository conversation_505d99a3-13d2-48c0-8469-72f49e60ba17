#!/bin/bash

# CV Generator Development Server
# This script starts the development server with hot reload

echo "🚀 Starting CV Generator Development Server..."
echo "📁 Project: CV Generator (Go + Templ)"
echo "🔄 Hot reload enabled with Air"
echo "🌐 Server will be available at: http://localhost:8080"
echo ""

# Check if Air is installed
if ! command -v air &> /dev/null; then
    echo "❌ Air is not installed. Installing..."
    go install github.com/air-verse/air@latest
    if [ $? -ne 0 ]; then
        echo "❌ Failed to install Air. Please install manually:"
        echo "   go install github.com/air-verse/air@latest"
        exit 1
    fi
    echo "✅ Air installed successfully!"
fi

# Check if templ is installed
if ! command -v templ &> /dev/null; then
    echo "❌ Templ is not installed. Installing..."
    go install github.com/a-h/templ/cmd/templ@latest
    if [ $? -ne 0 ]; then
        echo "❌ Failed to install Templ. Please install manually:"
        echo "   go install github.com/a-h/templ/cmd/templ@latest"
        exit 1
    fi
    echo "✅ Templ installed successfully!"
fi

# Generate initial templates
echo "🔧 Generating Templ templates..."
templ generate
if [ $? -ne 0 ]; then
    echo "❌ Failed to generate templates"
    exit 1
fi

# Create tmp directory if it doesn't exist
mkdir -p tmp

echo "✅ Setup complete! Starting development server..."
echo ""
echo "📝 Development Tips:"
echo "   • Edit .go files for backend changes"
echo "   • Edit .templ files for template changes"
echo "   • Edit static/ files for CSS/JS changes"
echo "   • Server will auto-reload on file changes"
echo ""
echo "🛑 Press Ctrl+C to stop the server"
echo ""

# Start Air
air

echo ""
echo "👋 Development server stopped. Happy coding!"
