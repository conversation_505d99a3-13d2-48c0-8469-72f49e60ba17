/* Reset and base styles */
* {
	margin: 0;
	padding: 0;
	box-sizing: border-box;
}

:root {
	--primary-color: #6366f1;
	--primary-dark: #4f46e5;
	--primary-light: #a5b4fc;
	--secondary-color: #f59e0b;
	--success-color: #10b981;
	--danger-color: #ef4444;
	--warning-color: #f59e0b;
	--info-color: #3b82f6;

	--text-primary: #1f2937;
	--text-secondary: #6b7280;
	--text-muted: #9ca3af;

	--bg-primary: #ffffff;
	--bg-secondary: #f9fafb;
	--bg-tertiary: #f3f4f6;

	--border-color: #e5e7eb;
	--border-light: #f3f4f6;

	--shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
	--shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
		0 2px 4px -1px rgba(0, 0, 0, 0.06);
	--shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
		0 4px 6px -2px rgba(0, 0, 0, 0.05);
	--shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
		0 10px 10px -5px rgba(0, 0, 0, 0.04);

	--radius-sm: 0.375rem;
	--radius-md: 0.5rem;
	--radius-lg: 0.75rem;
	--radius-xl: 1rem;

	--transition-fast: 0.15s ease-in-out;
	--transition-normal: 0.3s ease-in-out;
	--transition-slow: 0.5s ease-in-out;
}

html {
	scroll-behavior: smooth;
}

body {
	font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
		sans-serif;
	line-height: 1.6;
	color: var(--text-primary);
	background-color: var(--bg-secondary);
	font-size: 16px;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

/* Typography */
h1,
h2,
h3,
h4,
h5,
h6 {
	font-weight: 600;
	line-height: 1.2;
	color: var(--text-primary);
}

.gradient-text {
	background: linear-gradient(
		135deg,
		var(--primary-color),
		var(--secondary-color)
	);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	background-clip: text;
}

/* Navigation */
.navbar {
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(10px);
	border-bottom: 1px solid var(--border-light);
	color: var(--text-primary);
	padding: 1rem 0;
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 1000;
	transition: all var(--transition-normal);
}

.navbar.scrolled {
	box-shadow: var(--shadow-lg);
	background: rgba(255, 255, 255, 0.98);
}

.nav-container {
	max-width: 1200px;
	margin: 0 auto;
	padding: 0 2rem;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.nav-title {
	font-size: 1.5rem;
	font-weight: 700;
	background: linear-gradient(
		135deg,
		var(--primary-color),
		var(--secondary-color)
	);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	background-clip: text;
}

.nav-links {
	display: flex;
	gap: 2rem;
	align-items: center;
}

.nav-link {
	color: var(--text-secondary);
	text-decoration: none;
	font-weight: 500;
	padding: 0.5rem 1rem;
	border-radius: var(--radius-md);
	transition: all var(--transition-normal);
	position: relative;
}

.nav-link:hover {
	color: var(--primary-color);
	background: var(--bg-tertiary);
}

.nav-link.active {
	color: var(--primary-color);
	background: rgba(99, 102, 241, 0.1);
}

/* Main content */
.main-content {
	min-height: calc(100vh - 80px);
	padding-top: 80px; /* Account for fixed navbar */
}

/* Hero section */
.hero {
	position: relative;
	background: linear-gradient(
		135deg,
		var(--primary-color) 0%,
		var(--primary-dark) 100%
	);
	color: white;
	padding: 8rem 0 6rem;
	text-align: center;
	overflow: hidden;
}

.hero-background {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	overflow: hidden;
}

.hero-shapes {
	position: absolute;
	width: 100%;
	height: 100%;
}

.shape {
	position: absolute;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.1);
	animation: float 6s ease-in-out infinite;
}

.shape-1 {
	width: 200px;
	height: 200px;
	top: 10%;
	left: 10%;
	animation-delay: 0s;
}

.shape-2 {
	width: 150px;
	height: 150px;
	top: 60%;
	right: 15%;
	animation-delay: 2s;
}

.shape-3 {
	width: 100px;
	height: 100px;
	bottom: 20%;
	left: 60%;
	animation-delay: 4s;
}

@keyframes float {
	0%,
	100% {
		transform: translateY(0px) rotate(0deg);
	}
	50% {
		transform: translateY(-20px) rotate(180deg);
	}
}

.hero-content {
	position: relative;
	z-index: 2;
	max-width: 800px;
	margin: 0 auto;
	padding: 0 2rem;
}

.hero-badge {
	display: inline-block;
	background: rgba(255, 255, 255, 0.2);
	backdrop-filter: blur(10px);
	padding: 0.5rem 1.5rem;
	border-radius: 50px;
	margin-bottom: 2rem;
	border: 1px solid rgba(255, 255, 255, 0.3);
}

.badge-text {
	font-size: 0.9rem;
	font-weight: 500;
}

.hero-title {
	font-size: clamp(2.5rem, 5vw, 4rem);
	margin-bottom: 1.5rem;
	font-weight: 800;
	line-height: 1.1;
}

.hero-subtitle {
	font-size: 1.25rem;
	margin-bottom: 3rem;
	opacity: 0.9;
	line-height: 1.6;
	max-width: 600px;
	margin-left: auto;
	margin-right: auto;
}

.hero-buttons {
	display: flex;
	gap: 1.5rem;
	justify-content: center;
	flex-wrap: wrap;
	margin-bottom: 4rem;
}

.hero-stats {
	display: flex;
	gap: 3rem;
	justify-content: center;
	flex-wrap: wrap;
	margin-top: 4rem;
}

.stat {
	text-align: center;
}

.stat-number {
	display: block;
	font-size: 2rem;
	font-weight: 700;
	margin-bottom: 0.5rem;
}

.stat-label {
	font-size: 0.9rem;
	opacity: 0.8;
}

/* Buttons */
.btn {
	display: inline-flex;
	align-items: center;
	gap: 0.5rem;
	padding: 0.75rem 1.5rem;
	border: none;
	border-radius: var(--radius-lg);
	text-decoration: none;
	font-weight: 600;
	cursor: pointer;
	transition: all var(--transition-normal);
	font-size: 1rem;
	position: relative;
	overflow: hidden;
	white-space: nowrap;
}

.btn:before {
	content: "";
	position: absolute;
	top: 0;
	left: -100%;
	width: 100%;
	height: 100%;
	background: linear-gradient(
		90deg,
		transparent,
		rgba(255, 255, 255, 0.2),
		transparent
	);
	transition: left 0.5s;
}

.btn:hover:before {
	left: 100%;
}

.btn-primary {
	background: linear-gradient(
		135deg,
		var(--primary-color),
		var(--primary-dark)
	);
	color: white;
	box-shadow: var(--shadow-md);
}

.btn-primary:hover {
	transform: translateY(-2px);
	box-shadow: var(--shadow-lg);
	background: linear-gradient(
		135deg,
		var(--primary-dark),
		var(--primary-color)
	);
}

.btn-secondary {
	background: var(--text-secondary);
	color: white;
	box-shadow: var(--shadow-md);
}

.btn-secondary:hover {
	background: var(--text-primary);
	transform: translateY(-2px);
	box-shadow: var(--shadow-lg);
}

.btn-outline {
	background: transparent;
	color: white;
	border: 2px solid rgba(255, 255, 255, 0.3);
	backdrop-filter: blur(10px);
}

.btn-outline:hover {
	background: rgba(255, 255, 255, 0.1);
	border-color: rgba(255, 255, 255, 0.5);
	transform: translateY(-2px);
}

.btn-danger {
	background: var(--danger-color);
	color: white;
	box-shadow: var(--shadow-md);
}

.btn-danger:hover {
	background: #dc2626;
	transform: translateY(-2px);
	box-shadow: var(--shadow-lg);
}

.btn-large {
	padding: 1rem 2rem;
	font-size: 1.1rem;
}

.btn-sm {
	padding: 0.5rem 1rem;
	font-size: 0.875rem;
}

/* Features section */
.features {
	padding: 8rem 0;
	background: var(--bg-primary);
}

.container {
	max-width: 1200px;
	margin: 0 auto;
	padding: 0 2rem;
}

.section-header {
	text-align: center;
	margin-bottom: 4rem;
}

.section-title {
	font-size: clamp(2rem, 4vw, 3rem);
	margin-bottom: 1rem;
	color: var(--text-primary);
	font-weight: 700;
}

.section-subtitle {
	font-size: 1.2rem;
	color: var(--text-secondary);
	max-width: 600px;
	margin: 0 auto;
	line-height: 1.6;
}

.features-grid {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
	gap: 2rem;
}

.feature-card {
	background: var(--bg-primary);
	padding: 2.5rem;
	border-radius: var(--radius-xl);
	box-shadow: var(--shadow-md);
	transition: all var(--transition-normal);
	border: 1px solid var(--border-light);
	position: relative;
	overflow: hidden;
}

.feature-card:before {
	content: "";
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	height: 4px;
	background: linear-gradient(
		135deg,
		var(--primary-color),
		var(--secondary-color)
	);
	transform: scaleX(0);
	transition: transform var(--transition-normal);
}

.feature-card:hover:before {
	transform: scaleX(1);
}

.feature-card:hover {
	transform: translateY(-8px);
	box-shadow: var(--shadow-xl);
	border-color: var(--primary-light);
}

.feature-icon {
	width: 80px;
	height: 80px;
	background: linear-gradient(
		135deg,
		var(--primary-color),
		var(--primary-light)
	);
	border-radius: var(--radius-xl);
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 1.5rem;
	position: relative;
}

.feature-icon i {
	font-size: 2rem;
	color: white;
}

.feature-card h3 {
	font-size: 1.5rem;
	margin-bottom: 1rem;
	color: var(--text-primary);
	font-weight: 600;
}

.feature-card p {
	color: var(--text-secondary);
	line-height: 1.6;
	margin-bottom: 1.5rem;
}

.feature-link {
	margin-top: auto;
}

.feature-link a {
	color: var(--primary-color);
	text-decoration: none;
	font-weight: 500;
	display: inline-flex;
	align-items: center;
	gap: 0.5rem;
	transition: all var(--transition-normal);
}

.feature-link a:hover {
	color: var(--primary-dark);
	gap: 0.75rem;
}

.feature-link i {
	font-size: 0.875rem;
	transition: transform var(--transition-normal);
}

.feature-link a:hover i {
	transform: translateX(4px);
}

/* CTA Section */
.cta-section {
	background: linear-gradient(
		135deg,
		var(--primary-color),
		var(--primary-dark)
	);
	color: white;
	padding: 6rem 0;
	text-align: center;
}

.cta-content h2 {
	font-size: clamp(2rem, 4vw, 3rem);
	margin-bottom: 1rem;
	font-weight: 700;
	color: white;
}

.cta-content p {
	font-size: 1.2rem;
	margin-bottom: 2rem;
	opacity: 0.9;
	max-width: 600px;
	margin-left: auto;
	margin-right: auto;
}

/* Enhanced Form Styles */
.form-page {
	background: var(--bg-secondary);
	min-height: 100vh;
	padding-top: 80px;
}

.form-header {
	background: linear-gradient(
		135deg,
		var(--primary-color),
		var(--primary-dark)
	);
	color: white;
	padding: 4rem 0 2rem;
	text-align: center;
}

.form-title {
	font-size: clamp(2rem, 4vw, 3rem);
	margin-bottom: 1rem;
	font-weight: 700;
	color: white;
}

.form-subtitle {
	font-size: 1.2rem;
	margin-bottom: 3rem;
	opacity: 0.9;
	max-width: 600px;
	margin-left: auto;
	margin-right: auto;
}

.progress-bar {
	width: 100%;
	max-width: 600px;
	height: 8px;
	background: rgba(255, 255, 255, 0.2);
	border-radius: 4px;
	margin: 0 auto 2rem;
	overflow: hidden;
}

.progress-fill {
	height: 100%;
	background: linear-gradient(90deg, var(--secondary-color), #fbbf24);
	border-radius: 4px;
	width: 20%;
	transition: width var(--transition-normal);
}

.progress-steps {
	display: flex;
	justify-content: center;
	gap: 2rem;
	flex-wrap: wrap;
}

.step {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 0.5rem;
	opacity: 0.6;
	transition: all var(--transition-normal);
}

.step.active {
	opacity: 1;
}

.step.completed {
	opacity: 1;
}

.step-number {
	width: 40px;
	height: 40px;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.2);
	display: flex;
	align-items: center;
	justify-content: center;
	font-weight: 600;
	transition: all var(--transition-normal);
}

.step.active .step-number {
	background: var(--secondary-color);
	color: white;
}

.step.completed .step-number {
	background: var(--success-color);
	color: white;
}

.step-label {
	font-size: 0.875rem;
	font-weight: 500;
}

.form-container {
	max-width: 1400px;
	margin: 0 auto;
	padding: 2rem;
	display: grid;
	grid-template-columns: 300px 1fr;
	gap: 3rem;
	align-items: start;
}

.form-sidebar {
	background: var(--bg-primary);
	padding: 2rem;
	border-radius: var(--radius-xl);
	box-shadow: var(--shadow-lg);
	position: sticky;
	top: 100px;
	border: 1px solid var(--border-light);
}

.sidebar-content h3 {
	color: var(--text-primary);
	margin-bottom: 1.5rem;
	font-size: 1.25rem;
	font-weight: 600;
}

.tip-card {
	display: flex;
	gap: 1rem;
	padding: 1.5rem;
	background: var(--bg-tertiary);
	border-radius: var(--radius-lg);
	margin-bottom: 1rem;
	border-left: 4px solid var(--primary-color);
}

.tip-card i {
	color: var(--primary-color);
	font-size: 1.25rem;
	margin-top: 0.25rem;
}

.tip-card h4 {
	color: var(--text-primary);
	font-size: 1rem;
	margin-bottom: 0.5rem;
	font-weight: 600;
}

.tip-card p {
	color: var(--text-secondary);
	font-size: 0.875rem;
	line-height: 1.5;
	margin: 0;
}

.form-main {
	background: var(--bg-primary);
	border-radius: var(--radius-xl);
	box-shadow: var(--shadow-lg);
	overflow: hidden;
	border: 1px solid var(--border-light);
}

.form-step {
	display: none;
	padding: 3rem;
}

.form-step.active {
	display: block;
	animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
	from {
		opacity: 0;
		transform: translateY(20px);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

.step-header {
	text-align: center;
	margin-bottom: 3rem;
	padding-bottom: 2rem;
	border-bottom: 1px solid var(--border-light);
}

.step-header h2 {
	color: var(--text-primary);
	font-size: 1.75rem;
	margin-bottom: 0.5rem;
	font-weight: 600;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 0.75rem;
}

.step-header h2 i {
	color: var(--primary-color);
}

.step-header p {
	color: var(--text-secondary);
	font-size: 1.1rem;
}

.form-sidebar h2 {
	margin-bottom: 1.5rem;
	color: #333;
}

.form-nav {
	display: flex;
	flex-direction: column;
	gap: 0.5rem;
	margin-bottom: 2rem;
}

.nav-item {
	padding: 0.75rem 1rem;
	text-decoration: none;
	color: #666;
	border-radius: 5px;
	transition: all 0.3s ease;
}

.nav-item:hover,
.nav-item.active {
	background-color: #667eea;
	color: white;
}

.form-actions {
	display: flex;
	flex-direction: column;
	gap: 1rem;
}

.form-content {
	background: white;
	padding: 2rem;
	border-radius: 10px;
	box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.form-section {
	margin-bottom: 3rem;
}

.form-section h3 {
	font-size: 1.5rem;
	margin-bottom: 1.5rem;
	color: #333;
	border-bottom: 2px solid #667eea;
	padding-bottom: 0.5rem;
}

.section-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 1.5rem;
}

.form-grid {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
	gap: 1rem;
	margin-bottom: 1rem;
}

.form-group {
	display: flex;
	flex-direction: column;
	margin-bottom: 1.5rem;
}

.form-group label {
	font-weight: 600;
	margin-bottom: 0.75rem;
	color: var(--text-primary);
	font-size: 0.95rem;
}

.form-group input,
.form-group select,
.form-group textarea {
	padding: 1rem;
	border: 2px solid var(--border-color);
	border-radius: var(--radius-lg);
	font-size: 1rem;
	transition: all var(--transition-normal);
	background: var(--bg-primary);
	color: var(--text-primary);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
	color: var(--text-muted);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
	outline: none;
	border-color: var(--primary-color);
	box-shadow: 0 0 0 4px rgba(99, 102, 241, 0.1);
	transform: translateY(-1px);
}

.form-group input:valid,
.form-group textarea:valid {
	border-color: var(--success-color);
}

.field-hint {
	font-size: 0.875rem;
	color: var(--text-muted);
	margin-top: 0.5rem;
	line-height: 1.4;
}

.checkbox-group {
	margin: 1.5rem 0;
}

.checkbox-label {
	flex-direction: row !important;
	align-items: center;
	gap: 0.75rem;
	cursor: pointer;
	font-weight: 500;
	color: var(--text-primary);
	padding: 1rem;
	background: var(--bg-tertiary);
	border-radius: var(--radius-lg);
	border: 2px solid transparent;
	transition: all var(--transition-normal);
}

.checkbox-label:hover {
	background: var(--bg-secondary);
	border-color: var(--primary-light);
}

.checkbox-label input[type="checkbox"] {
	width: auto;
	margin: 0;
	position: relative;
	appearance: none;
	width: 20px;
	height: 20px;
	border: 2px solid var(--border-color);
	border-radius: var(--radius-sm);
	background: var(--bg-primary);
	cursor: pointer;
	transition: all var(--transition-normal);
}

.checkbox-label input[type="checkbox"]:checked {
	background: var(--primary-color);
	border-color: var(--primary-color);
}

.checkbox-label input[type="checkbox"]:checked::after {
	content: "✓";
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	color: white;
	font-size: 12px;
	font-weight: bold;
}

.form-navigation {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 2rem 3rem;
	background: var(--bg-tertiary);
	border-top: 1px solid var(--border-light);
}

.form-actions {
	display: flex;
	justify-content: center;
	gap: 1rem;
	padding: 2rem 3rem;
	background: var(--bg-tertiary);
	border-top: 1px solid var(--border-light);
}

.form-item {
	background: #f8f9fa;
	padding: 1.5rem;
	border-radius: 8px;
	margin-bottom: 1.5rem;
	border: 1px solid #e9ecef;
}

.item-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 1rem;
}

.item-header h4 {
	color: #333;
	font-size: 1.2rem;
}

.skill-item,
.language-item {
	background: #f8f9fa;
	padding: 1rem;
	border-radius: 5px;
	margin-bottom: 1rem;
}

/* Preview container */
.preview-container {
	background: white;
	padding: 2rem;
	border-radius: 10px;
	box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
	position: sticky;
	top: 2rem;
	max-height: calc(100vh - 4rem);
	overflow-y: auto;
}

.preview-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 1.5rem;
	padding-bottom: 1rem;
	border-bottom: 1px solid #eee;
}

.preview-header h3 {
	color: #333;
}

/* Footer */
.footer {
	background: #333;
	color: white;
	text-align: center;
	padding: 2rem 0;
	margin-top: auto;
}

/* Responsive design */
@media (max-width: 1200px) {
	.form-container {
		grid-template-columns: 250px 1fr;
	}

	.preview-container {
		display: none !important;
	}
}

@media (max-width: 768px) {
	.form-container {
		grid-template-columns: 1fr;
	}

	.form-sidebar {
		position: static;
	}

	.form-nav {
		flex-direction: row;
		flex-wrap: wrap;
	}

	.hero-content h1 {
		font-size: 2rem;
	}

	.features-grid {
		grid-template-columns: 1fr;
	}

	.form-grid {
		grid-template-columns: 1fr;
	}

	.hero-buttons {
		flex-direction: column;
		align-items: center;
	}
}

/* CV Styles */
.cv-container {
	max-width: 800px;
	margin: 0 auto;
	background: white;
	padding: 2rem;
	box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
	font-family: "Georgia", serif;
	line-height: 1.5;
}

.cv-header {
	text-align: center;
	margin-bottom: 2rem;
	padding-bottom: 1.5rem;
	border-bottom: 2px solid #333;
}

.cv-header .name {
	font-size: 2.5rem;
	font-weight: bold;
	color: #333;
	margin-bottom: 0.5rem;
}

.cv-header .summary {
	font-size: 1.1rem;
	color: #666;
	margin-bottom: 1rem;
	font-style: italic;
}

.contact-info {
	display: flex;
	justify-content: center;
	flex-wrap: wrap;
	gap: 1rem;
}

.contact-item {
	display: flex;
	align-items: center;
	gap: 0.5rem;
	color: #555;
}

.contact-item i {
	color: #667eea;
	width: 16px;
}

.contact-item a {
	color: #667eea;
	text-decoration: none;
}

.contact-item a:hover {
	text-decoration: underline;
}

.cv-section {
	margin-bottom: 2rem;
}

.section-title {
	font-size: 1.5rem;
	color: #333;
	margin-bottom: 1rem;
	padding-bottom: 0.5rem;
	border-bottom: 1px solid #ddd;
	text-transform: uppercase;
	letter-spacing: 1px;
}

.experience-item,
.education-item,
.project-item,
.certification-item {
	margin-bottom: 1.5rem;
	padding-bottom: 1rem;
	border-bottom: 1px solid #eee;
}

.experience-item:last-child,
.education-item:last-child,
.project-item:last-child,
.certification-item:last-child {
	border-bottom: none;
}

.item-header {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	margin-bottom: 0.5rem;
}

.job-title,
.degree,
.project-name,
.cert-name {
	font-size: 1.2rem;
	font-weight: bold;
	color: #333;
}

.date-range,
.issue-date {
	color: #666;
	font-weight: 500;
	white-space: nowrap;
}

.company-location,
.institution-location {
	color: #555;
	margin-bottom: 0.5rem;
}

.company,
.institution,
.issuer {
	font-weight: 500;
}

.location {
	color: #777;
}

.description {
	color: #555;
	margin-bottom: 0.5rem;
}

.achievements {
	list-style: none;
	margin: 0.5rem 0;
}

.achievements li {
	position: relative;
	padding-left: 1rem;
	margin-bottom: 0.25rem;
	color: #555;
}

.achievements li:before {
	content: "•";
	color: #667eea;
	font-weight: bold;
	position: absolute;
	left: 0;
}

.gpa {
	color: #666;
	font-style: italic;
	margin-bottom: 0.5rem;
}

.skills-container {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
	gap: 1rem;
}

.skill-category {
	margin-bottom: 1rem;
}

.category-title {
	font-weight: bold;
	color: #333;
	margin-bottom: 0.5rem;
	font-size: 1.1rem;
}

.skills-list {
	display: flex;
	flex-wrap: wrap;
	gap: 0.5rem;
}

.skill-item {
	background: #f8f9fa;
	padding: 0.25rem 0.75rem;
	border-radius: 15px;
	font-size: 0.9rem;
	color: #555;
	border: 1px solid #e9ecef;
}

.skill-item[data-proficiency="Expert"] {
	background: #d4edda;
	border-color: #c3e6cb;
	color: #155724;
}

.skill-item[data-proficiency="Advanced"] {
	background: #cce5ff;
	border-color: #b3d9ff;
	color: #004085;
}

.skill-item[data-proficiency="Intermediate"] {
	background: #fff3cd;
	border-color: #ffeaa7;
	color: #856404;
}

.technologies {
	margin: 0.5rem 0;
	color: #555;
}

.tech {
	background: #e9ecef;
	padding: 0.2rem 0.5rem;
	border-radius: 3px;
	font-size: 0.9rem;
}

.project-links {
	margin-top: 0.5rem;
}

.project-link {
	display: inline-block;
	margin-right: 1rem;
	color: #667eea;
	text-decoration: none;
	font-size: 0.9rem;
}

.project-link:hover {
	text-decoration: underline;
}

.project-link i {
	margin-right: 0.25rem;
}

.credential-id {
	color: #666;
	font-size: 0.9rem;
	margin-bottom: 0.5rem;
}

.cert-link {
	color: #667eea;
	text-decoration: none;
	font-size: 0.9rem;
}

.cert-link:hover {
	text-decoration: underline;
}

.cert-link i {
	margin-right: 0.25rem;
}

.languages-container {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
	gap: 1rem;
}

.language-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 0.5rem;
	background: #f8f9fa;
	border-radius: 5px;
}

.language-name {
	font-weight: 500;
	color: #333;
}

.language-proficiency {
	color: #666;
	font-size: 0.9rem;
}

/* Print styles */
@media print {
	.navbar,
	.footer,
	.form-sidebar,
	.preview-header,
	.btn {
		display: none !important;
	}

	.cv-container {
		box-shadow: none;
		margin: 0;
		padding: 0;
	}

	.form-container {
		grid-template-columns: 1fr;
	}

	.contact-info {
		font-size: 0.9rem;
	}

	.cv-section {
		page-break-inside: avoid;
	}
}

/* Notification Styles */
.notification {
	position: fixed;
	top: 100px;
	right: 2rem;
	background: var(--bg-primary);
	border: 1px solid var(--border-color);
	border-radius: var(--radius-lg);
	padding: 1rem 1.5rem;
	box-shadow: var(--shadow-lg);
	display: flex;
	align-items: center;
	gap: 0.75rem;
	z-index: 1000;
	min-width: 300px;
	animation: slideInRight 0.3s ease-out;
}

.notification-error {
	border-color: var(--danger-color);
	background: #fef2f2;
}

.notification-error i {
	color: var(--danger-color);
}

.notification-info {
	border-color: var(--info-color);
	background: #eff6ff;
}

.notification-info i {
	color: var(--info-color);
}

.notification-close {
	background: none;
	border: none;
	color: var(--text-muted);
	cursor: pointer;
	padding: 0.25rem;
	margin-left: auto;
	border-radius: var(--radius-sm);
	transition: all var(--transition-normal);
}

.notification-close:hover {
	background: var(--bg-tertiary);
	color: var(--text-primary);
}

@keyframes slideInRight {
	from {
		transform: translateX(100%);
		opacity: 0;
	}
	to {
		transform: translateX(0);
		opacity: 1;
	}
}

/* Mobile Responsive Improvements */
@media (max-width: 768px) {
	.form-container {
		grid-template-columns: 1fr;
		padding: 1rem;
	}

	.form-sidebar {
		position: static;
		order: 2;
		margin-top: 2rem;
	}

	.progress-steps {
		gap: 1rem;
	}

	.step-label {
		display: none;
	}

	.form-step {
		padding: 2rem 1.5rem;
	}

	.form-grid {
		grid-template-columns: 1fr;
	}

	.notification {
		right: 1rem;
		left: 1rem;
		min-width: auto;
	}
}
