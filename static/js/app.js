// CV Generator JavaScript

// Initialize everything when DOM is loaded
document.addEventListener("DOMContentLoaded", function () {
	// Initialize navbar scroll effects
	initNavbarEffects();

	// Initialize smooth scrolling
	initSmoothScrolling();

	// Initialize form navigation
	initFormNavigation();

	// Initialize dynamic form sections
	initDynamicSections();

	// Initialize form validation
	initFormValidation();

	// Initialize animations
	initAnimations();

	// Initialize multi-step form
	initMultiStepForm();
});

// Navbar scroll effects
function initNavbarEffects() {
	const navbar = document.querySelector(".navbar");
	if (!navbar) return;

	let lastScrollY = window.scrollY;

	window.addEventListener("scroll", () => {
		const currentScrollY = window.scrollY;

		// Add scrolled class when scrolling down
		if (currentScrollY > 50) {
			navbar.classList.add("scrolled");
		} else {
			navbar.classList.remove("scrolled");
		}

		lastScrollY = currentScrollY;
	});
}

// Smooth scrolling for anchor links
function initSmoothScrolling() {
	document.querySelectorAll('a[href^="#"]').forEach((anchor) => {
		anchor.addEventListener("click", function (e) {
			e.preventDefault();
			const target = document.querySelector(this.getAttribute("href"));
			if (target) {
				const offsetTop = target.offsetTop - 80; // Account for fixed navbar
				window.scrollTo({
					top: offsetTop,
					behavior: "smooth",
				});
			}
		});
	});
}

// Initialize animations
function initAnimations() {
	// Add fade-in animation to elements when they come into view
	const observerOptions = {
		threshold: 0.1,
		rootMargin: "0px 0px -50px 0px",
	};

	const observer = new IntersectionObserver((entries) => {
		entries.forEach((entry) => {
			if (entry.isIntersecting) {
				entry.target.style.opacity = "1";
				entry.target.style.transform = "translateY(0)";
			}
		});
	}, observerOptions);

	// Observe feature cards and other elements
	document
		.querySelectorAll(".feature-card, .hero-content, .cta-content")
		.forEach((el) => {
			el.style.opacity = "0";
			el.style.transform = "translateY(30px)";
			el.style.transition = "opacity 0.6s ease, transform 0.6s ease";
			observer.observe(el);
		});
}

function initFormNavigation() {
	const navItems = document.querySelectorAll(".nav-item");
	const sections = document.querySelectorAll(".form-section");

	navItems.forEach((item) => {
		item.addEventListener("click", function (e) {
			e.preventDefault();

			// Remove active class from all nav items
			navItems.forEach((nav) => nav.classList.remove("active"));

			// Add active class to clicked item
			this.classList.add("active");

			// Get target section
			const targetId = this.getAttribute("href").substring(1);
			const targetSection = document.getElementById(targetId);

			if (targetSection) {
				targetSection.scrollIntoView({ behavior: "smooth" });
			}
		});
	});

	// Update active nav item on scroll
	window.addEventListener("scroll", function () {
		let current = "";
		sections.forEach((section) => {
			const sectionTop = section.offsetTop;
			const sectionHeight = section.clientHeight;
			if (scrollY >= sectionTop - 200) {
				current = section.getAttribute("id");
			}
		});

		navItems.forEach((item) => {
			item.classList.remove("active");
			if (item.getAttribute("href") === "#" + current) {
				item.classList.add("active");
			}
		});
	});
}

function initDynamicSections() {
	// Add event listeners for dynamic form sections
	window.addExperience = addExperience;
	window.addEducation = addEducation;
	window.addSkill = addSkill;
	window.addProject = addProject;
	window.addCertification = addCertification;
	window.addLanguage = addLanguage;
	window.removeItem = removeItem;
	window.previewCV = previewCV;
	window.generatePDF = generatePDF;
	window.closePreview = closePreview;
}

function addExperience() {
	const container = document.getElementById("experience-list");
	const index = container.children.length;

	const html = `
        <div class="form-item" data-index="${index}">
            <div class="item-header">
                <h4>Experience ${index + 1}</h4>
                <button type="button" class="btn btn-danger btn-sm" onclick="removeItem(this)">Remove</button>
            </div>
            <div class="form-grid">
                <div class="form-group">
                    <label for="job_title_${index}">Job Title *</label>
                    <input type="text" id="job_title_${index}" name="experiences[${index}].job_title" required/>
                </div>
                <div class="form-group">
                    <label for="company_${index}">Company *</label>
                    <input type="text" id="company_${index}" name="experiences[${index}].company" required/>
                </div>
                <div class="form-group">
                    <label for="location_${index}">Location</label>
                    <input type="text" id="location_${index}" name="experiences[${index}].location"/>
                </div>
                <div class="form-group">
                    <label for="start_date_${index}">Start Date *</label>
                    <input type="month" id="start_date_${index}" name="experiences[${index}].start_date" required/>
                </div>
                <div class="form-group">
                    <label for="end_date_${index}">End Date</label>
                    <input type="month" id="end_date_${index}" name="experiences[${index}].end_date"/>
                </div>
                <div class="form-group">
                    <label class="checkbox-label">
                        <input type="checkbox" name="experiences[${index}].current"/> Currently working here
                    </label>
                </div>
            </div>
            <div class="form-group">
                <label for="description_${index}">Description</label>
                <textarea id="description_${index}" name="experiences[${index}].description" rows="3"></textarea>
            </div>
            <div class="form-group">
                <label for="achievements_${index}">Key Achievements (one per line)</label>
                <textarea id="achievements_${index}" name="experiences[${index}].achievements" rows="3" placeholder="• Increased sales by 25%&#10;• Led a team of 5 developers&#10;• Implemented new system that reduced costs by 15%"></textarea>
            </div>
        </div>
    `;

	container.insertAdjacentHTML("beforeend", html);
}

function addEducation() {
	const container = document.getElementById("education-list");
	const index = container.children.length;

	const html = `
        <div class="form-item" data-index="${index}">
            <div class="item-header">
                <h4>Education ${index + 1}</h4>
                <button type="button" class="btn btn-danger btn-sm" onclick="removeItem(this)">Remove</button>
            </div>
            <div class="form-grid">
                <div class="form-group">
                    <label for="degree_${index}">Degree *</label>
                    <input type="text" id="degree_${index}" name="education[${index}].degree" required/>
                </div>
                <div class="form-group">
                    <label for="institution_${index}">Institution *</label>
                    <input type="text" id="institution_${index}" name="education[${index}].institution" required/>
                </div>
                <div class="form-group">
                    <label for="edu_location_${index}">Location</label>
                    <input type="text" id="edu_location_${index}" name="education[${index}].location"/>
                </div>
                <div class="form-group">
                    <label for="edu_start_date_${index}">Start Date *</label>
                    <input type="month" id="edu_start_date_${index}" name="education[${index}].start_date" required/>
                </div>
                <div class="form-group">
                    <label for="edu_end_date_${index}">End Date</label>
                    <input type="month" id="edu_end_date_${index}" name="education[${index}].end_date"/>
                </div>
                <div class="form-group">
                    <label for="gpa_${index}">GPA</label>
                    <input type="text" id="gpa_${index}" name="education[${index}].gpa"/>
                </div>
            </div>
            <div class="form-group">
                <label class="checkbox-label">
                    <input type="checkbox" name="education[${index}].current"/> Currently studying here
                </label>
            </div>
            <div class="form-group">
                <label for="edu_description_${index}">Description</label>
                <textarea id="edu_description_${index}" name="education[${index}].description" rows="2"></textarea>
            </div>
        </div>
    `;

	container.insertAdjacentHTML("beforeend", html);
}

function addSkill() {
	const container = document.getElementById("skills-list");
	const index = container.children.length;

	const html = `
        <div class="form-item skill-item" data-index="${index}">
            <div class="form-grid">
                <div class="form-group">
                    <label for="skill_name_${index}">Skill *</label>
                    <input type="text" id="skill_name_${index}" name="skills[${index}].name" required/>
                </div>
                <div class="form-group">
                    <label for="skill_category_${index}">Category</label>
                    <select id="skill_category_${index}" name="skills[${index}].category">
                        <option value="Programming">Programming</option>
                        <option value="Languages">Languages</option>
                        <option value="Tools">Tools</option>
                        <option value="Frameworks">Frameworks</option>
                        <option value="Other">Other</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="skill_proficiency_${index}">Proficiency</label>
                    <select id="skill_proficiency_${index}" name="skills[${index}].proficiency">
                        <option value="Beginner">Beginner</option>
                        <option value="Intermediate">Intermediate</option>
                        <option value="Advanced">Advanced</option>
                        <option value="Expert">Expert</option>
                    </select>
                </div>
                <div class="form-group">
                    <button type="button" class="btn btn-danger btn-sm" onclick="removeItem(this)">Remove</button>
                </div>
            </div>
        </div>
    `;

	container.insertAdjacentHTML("beforeend", html);
}

function addProject() {
	const container = document.getElementById("projects-list");
	const index = container.children.length;

	const html = `
        <div class="form-item" data-index="${index}">
            <div class="item-header">
                <h4>Project ${index + 1}</h4>
                <button type="button" class="btn btn-danger btn-sm" onclick="removeItem(this)">Remove</button>
            </div>
            <div class="form-grid">
                <div class="form-group">
                    <label for="project_name_${index}">Project Name *</label>
                    <input type="text" id="project_name_${index}" name="projects[${index}].name" required/>
                </div>
                <div class="form-group">
                    <label for="project_url_${index}">Live URL</label>
                    <input type="url" id="project_url_${index}" name="projects[${index}].url"/>
                </div>
                <div class="form-group">
                    <label for="project_github_${index}">GitHub URL</label>
                    <input type="url" id="project_github_${index}" name="projects[${index}].github_url"/>
                </div>
                <div class="form-group">
                    <label for="project_start_date_${index}">Start Date *</label>
                    <input type="month" id="project_start_date_${index}" name="projects[${index}].start_date" required/>
                </div>
                <div class="form-group">
                    <label for="project_end_date_${index}">End Date</label>
                    <input type="month" id="project_end_date_${index}" name="projects[${index}].end_date"/>
                </div>
                <div class="form-group">
                    <label class="checkbox-label">
                        <input type="checkbox" name="projects[${index}].current"/> Currently working on this
                    </label>
                </div>
            </div>
            <div class="form-group">
                <label for="project_description_${index}">Description *</label>
                <textarea id="project_description_${index}" name="projects[${index}].description" rows="3" required></textarea>
            </div>
            <div class="form-group">
                <label for="project_technologies_${index}">Technologies (comma-separated)</label>
                <input type="text" id="project_technologies_${index}" name="projects[${index}].technologies" placeholder="React, Node.js, MongoDB, Docker"/>
            </div>
        </div>
    `;

	container.insertAdjacentHTML("beforeend", html);
}

function addCertification() {
	const container = document.getElementById("certifications-list");
	const index = container.children.length;

	const html = `
        <div class="form-item" data-index="${index}">
            <div class="item-header">
                <h4>Certification ${index + 1}</h4>
                <button type="button" class="btn btn-danger btn-sm" onclick="removeItem(this)">Remove</button>
            </div>
            <div class="form-grid">
                <div class="form-group">
                    <label for="cert_name_${index}">Certification Name *</label>
                    <input type="text" id="cert_name_${index}" name="certifications[${index}].name" required/>
                </div>
                <div class="form-group">
                    <label for="cert_issuer_${index}">Issuer *</label>
                    <input type="text" id="cert_issuer_${index}" name="certifications[${index}].issuer" required/>
                </div>
                <div class="form-group">
                    <label for="cert_issue_date_${index}">Issue Date *</label>
                    <input type="month" id="cert_issue_date_${index}" name="certifications[${index}].issue_date" required/>
                </div>
                <div class="form-group">
                    <label for="cert_expiry_date_${index}">Expiry Date</label>
                    <input type="month" id="cert_expiry_date_${index}" name="certifications[${index}].expiry_date"/>
                </div>
                <div class="form-group">
                    <label for="cert_credential_id_${index}">Credential ID</label>
                    <input type="text" id="cert_credential_id_${index}" name="certifications[${index}].credential_id"/>
                </div>
                <div class="form-group">
                    <label for="cert_url_${index}">Verification URL</label>
                    <input type="url" id="cert_url_${index}" name="certifications[${index}].url"/>
                </div>
            </div>
        </div>
    `;

	container.insertAdjacentHTML("beforeend", html);
}

function addLanguage() {
	const container = document.getElementById("languages-list");
	const index = container.children.length;

	const html = `
        <div class="form-item language-item" data-index="${index}">
            <div class="form-grid">
                <div class="form-group">
                    <label for="lang_name_${index}">Language *</label>
                    <input type="text" id="lang_name_${index}" name="languages[${index}].name" required/>
                </div>
                <div class="form-group">
                    <label for="lang_proficiency_${index}">Proficiency</label>
                    <select id="lang_proficiency_${index}" name="languages[${index}].proficiency">
                        <option value="Native">Native</option>
                        <option value="Fluent">Fluent</option>
                        <option value="Conversational">Conversational</option>
                        <option value="Basic">Basic</option>
                    </select>
                </div>
                <div class="form-group">
                    <button type="button" class="btn btn-danger btn-sm" onclick="removeItem(this)">Remove</button>
                </div>
            </div>
        </div>
    `;

	container.insertAdjacentHTML("beforeend", html);
}

function removeItem(button) {
	const item = button.closest(".form-item");
	if (item) {
		item.remove();
		// Update indices for remaining items
		updateIndices(item.closest('[id$="-list"]'));
	}
}

function updateIndices(container) {
	const items = container.querySelectorAll(".form-item");
	items.forEach((item, index) => {
		item.setAttribute("data-index", index);
		// Update form field names and IDs
		const inputs = item.querySelectorAll("input, select, textarea");
		inputs.forEach((input) => {
			const name = input.getAttribute("name");
			const id = input.getAttribute("id");
			if (name) {
				const newName = name.replace(/\[\d+\]/, `[${index}]`);
				input.setAttribute("name", newName);
			}
			if (id) {
				const newId = id.replace(/_\d+$/, `_${index}`);
				input.setAttribute("id", newId);
			}
		});

		// Update labels
		const labels = item.querySelectorAll("label");
		labels.forEach((label) => {
			const forAttr = label.getAttribute("for");
			if (forAttr) {
				const newFor = forAttr.replace(/_\d+$/, `_${index}`);
				label.setAttribute("for", newFor);
			}
		});

		// Update header text
		const header = item.querySelector(".item-header h4");
		if (header) {
			const text = header.textContent;
			const newText = text.replace(/\d+/, index + 1);
			header.textContent = newText;
		}
	});
}

function previewCV() {
	const form = document.getElementById("cv-form");
	const formData = new FormData(form);

	fetch("/preview", {
		method: "POST",
		body: formData,
	})
		.then((response) => response.text())
		.then((html) => {
			const previewContainer = document.getElementById("preview-container");
			const previewContent = document.getElementById("preview-content");
			previewContent.innerHTML = html;
			previewContainer.style.display = "block";
		})
		.catch((error) => {
			console.error("Error:", error);
			alert("Error generating preview");
		});
}

function generatePDF() {
	const form = document.getElementById("cv-form");

	// Add format parameter for PDF
	const formatInput = document.createElement("input");
	formatInput.type = "hidden";
	formatInput.name = "format";
	formatInput.value = "pdf";
	form.appendChild(formatInput);

	form.action = "/generate";
	form.submit();
}

function closePreview() {
	const previewContainer = document.getElementById("preview-container");
	previewContainer.style.display = "none";
}

function initFormValidation() {
	const form = document.getElementById("cv-form");
	if (!form) return;

	// Add real-time validation
	const requiredFields = form.querySelectorAll(
		"input[required], textarea[required]"
	);
	requiredFields.forEach((field) => {
		field.addEventListener("blur", function () {
			validateField(this);
		});
	});
}

function validateField(field) {
	const isValid = field.checkValidity();
	if (isValid) {
		field.classList.remove("error");
	} else {
		field.classList.add("error");
	}
	return isValid;
}

// Multi-step form functionality
let currentStep = 1;
const totalSteps = 5;

function initMultiStepForm() {
	// Add global functions for navigation
	window.nextStep = nextStep;
	window.previousStep = previousStep;

	// Initialize current job checkbox behavior
	const currentJobCheckbox = document.getElementById("current_job");
	const endDateInput = document.getElementById("end_date");

	if (currentJobCheckbox && endDateInput) {
		currentJobCheckbox.addEventListener("change", function () {
			if (this.checked) {
				endDateInput.value = "";
				endDateInput.disabled = true;
				endDateInput.style.opacity = "0.5";
			} else {
				endDateInput.disabled = false;
				endDateInput.style.opacity = "1";
			}
		});
	}

	// Auto-save form data
	initAutoSave();

	// Update progress
	updateProgress();
}

function nextStep() {
	if (currentStep < totalSteps) {
		// Validate current step
		if (validateCurrentStep()) {
			// Hide current step
			document
				.querySelector(`[data-step="${currentStep}"]`)
				.classList.remove("active");

			// Mark current step as completed
			document
				.querySelector(`.step[data-step="${currentStep}"]`)
				.classList.add("completed");

			// Move to next step
			currentStep++;

			// Show next step
			document
				.querySelector(`[data-step="${currentStep}"]`)
				.classList.add("active");

			// Update step indicator
			document
				.querySelector(`.step[data-step="${currentStep}"]`)
				.classList.add("active");

			// Update navigation buttons
			updateNavigation();

			// Update progress
			updateProgress();

			// Scroll to top of form
			document
				.querySelector(".form-main")
				.scrollIntoView({ behavior: "smooth" });
		}
	}
}

function previousStep() {
	if (currentStep > 1) {
		// Hide current step
		document
			.querySelector(`[data-step="${currentStep}"]`)
			.classList.remove("active");
		document
			.querySelector(`.step[data-step="${currentStep}"]`)
			.classList.remove("active");

		// Move to previous step
		currentStep--;

		// Show previous step
		document
			.querySelector(`[data-step="${currentStep}"]`)
			.classList.add("active");
		document
			.querySelector(`.step[data-step="${currentStep}"]`)
			.classList.add("active");

		// Update navigation buttons
		updateNavigation();

		// Update progress
		updateProgress();

		// Scroll to top of form
		document.querySelector(".form-main").scrollIntoView({ behavior: "smooth" });
	}
}

function updateNavigation() {
	const prevBtn = document.getElementById("prev-btn");
	const nextBtn = document.getElementById("next-btn");
	const previewBtn = document.getElementById("preview-btn");
	const finalActions = document.getElementById("final-actions");

	if (prevBtn) {
		prevBtn.style.display = currentStep > 1 ? "inline-flex" : "none";
	}

	if (nextBtn) {
		nextBtn.style.display = currentStep < totalSteps ? "inline-flex" : "none";
	}

	if (previewBtn) {
		previewBtn.style.display =
			currentStep === totalSteps ? "inline-flex" : "none";
	}

	if (finalActions) {
		finalActions.style.display = currentStep === totalSteps ? "flex" : "none";
	}
}

function updateProgress() {
	const progressFill = document.getElementById("progress-fill");
	if (progressFill) {
		const progress = (currentStep / totalSteps) * 100;
		progressFill.style.width = `${progress}%`;
	}
}

function validateCurrentStep() {
	const currentStepElement = document.querySelector(
		`[data-step="${currentStep}"]`
	);
	const requiredFields = currentStepElement.querySelectorAll(
		"input[required], textarea[required]"
	);

	let isValid = true;

	requiredFields.forEach((field) => {
		if (!field.value.trim()) {
			field.style.borderColor = "var(--danger-color)";
			field.focus();
			isValid = false;
		} else {
			field.style.borderColor = "var(--success-color)";
		}
	});

	if (!isValid) {
		// Show error message
		showNotification("Please fill in all required fields", "error");
	}

	return isValid;
}

function initAutoSave() {
	const form = document.getElementById("cv-form");
	if (!form) return;

	// Save form data to localStorage every 30 seconds
	setInterval(() => {
		const formData = new FormData(form);
		const data = {};
		for (let [key, value] of formData.entries()) {
			data[key] = value;
		}
		localStorage.setItem("cv-form-data", JSON.stringify(data));
	}, 30000);

	// Load saved data on page load
	const savedData = localStorage.getItem("cv-form-data");
	if (savedData) {
		try {
			const data = JSON.parse(savedData);
			Object.keys(data).forEach((key) => {
				const field = form.querySelector(`[name="${key}"]`);
				if (field) {
					field.value = data[key];
				}
			});
		} catch (e) {
			console.log("Error loading saved form data:", e);
		}
	}
}

function showNotification(message, type = "info") {
	// Create notification element
	const notification = document.createElement("div");
	notification.className = `notification notification-${type}`;
	notification.innerHTML = `
		<i class="fas fa-${
			type === "error" ? "exclamation-circle" : "info-circle"
		}"></i>
		<span>${message}</span>
		<button onclick="this.parentElement.remove()" class="notification-close">
			<i class="fas fa-times"></i>
		</button>
	`;

	// Add to page
	document.body.appendChild(notification);

	// Auto remove after 5 seconds
	setTimeout(() => {
		if (notification.parentElement) {
			notification.remove();
		}
	}, 5000);
}
