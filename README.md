# 📄 Professional CV Generator

A modern, responsive CV generator built with **Go** and **Templ** that creates beautiful, professional CVs in minutes.

![CV Generator](https://img.shields.io/badge/Go-1.21+-blue.svg)
![Templ](https://img.shields.io/badge/Templ-Latest-green.svg)
![License](https://img.shields.io/badge/License-MIT-yellow.svg)

## ✨ Features

- 🎨 **Modern UI/UX** - Beautiful, responsive design with smooth animations
- 📝 **Multi-step Form** - Intuitive step-by-step CV creation process
- 👀 **Live Preview** - See your CV update in real-time
- 📱 **Mobile Friendly** - Works perfectly on all devices
- 🔄 **Auto-save** - Never lose your progress
- 📄 **PDF Export** - Download as high-quality PDF (requires Chrome/Chromium)
- 🌐 **HTML Export** - Always available as styled HTML
- 🚀 **Fast & Lightweight** - Built with Go for optimal performance
- 🔒 **Privacy First** - No data stored on servers, everything client-side

## 🚀 Quick Start

### Development Mode (with Hot Reload)

```bash
# Clone the repository
git clone <repository-url>
cd cv-generator

# Start development server with hot reload
./dev.sh
```

The development server will start at `http://localhost:8080` with automatic reloading when you make changes.

### Production Mode

```bash
# Build and run production server
./prod.sh
```

### Manual Setup

```bash
# Install dependencies
go mod tidy

# Install Templ CLI
go install github.com/a-h/templ/cmd/templ@latest

# Generate templates
templ generate

# Run the application
go run main.go
```

## 🛠️ Development

### Prerequisites

- **Go 1.21+**
- **Templ CLI** (automatically installed by dev script)
- **Air** (for hot reload, automatically installed by dev script)

### Project Structure

```
cv-generator/
├── main.go                    # Application entry point
├── internal/
│   ├── handlers/             # HTTP request handlers
│   ├── models/              # Data models and structures
│   ├── services/            # Business logic (PDF generation, etc.)
│   └── templates/           # Templ template files
├── static/
│   ├── css/                 # Stylesheets
│   └── js/                  # JavaScript files
├── dev.sh                   # Development server script
├── prod.sh                  # Production build script
└── .air.toml               # Air configuration for hot reload
```

### Hot Reload Development

The project uses [Air](https://github.com/air-verse/air) for hot reloading during development:

- **Go files** - Server restarts automatically
- **Templ files** - Templates regenerated and server restarted
- **Static files** - Changes reflected immediately

### Making Changes

1. **Backend Logic**: Edit files in `internal/`
2. **Templates**: Edit `.templ` files in `internal/templates/`
3. **Styling**: Edit `static/css/style.css`
4. **JavaScript**: Edit `static/js/app.js`

## 📋 Usage

1. **Start the server** using `./dev.sh` or `./prod.sh`
2. **Open your browser** to `http://localhost:8080`
3. **Click "Create Your CV"** to start the form
4. **Fill out the multi-step form** with your information
5. **Preview your CV** at any time
6. **Generate PDF** or download as HTML

### CV Sections

- **Personal Information** - Name, contact details, professional summary
- **Professional Experience** - Work history with achievements
- **Education** - Academic background
- **Skills** - Technical and professional skills
- **Projects** - Portfolio projects and accomplishments
- **Certifications** - Professional certifications (optional)
- **Languages** - Language proficiencies (optional)

## 🎨 Customization

### Styling

The application uses CSS custom properties (variables) for easy theming:

```css
:root {
  --primary-color: #6366f1;
  --secondary-color: #f59e0b;
  --text-primary: #1f2937;
  /* ... more variables */
}
```

### Templates

Templates are written in [Templ](https://templ.guide/), a type-safe template language for Go:

```go
templ CVHeader(info models.PersonalInfo) {
  <header class="cv-header">
    <h1>{info.FullName}</h1>
    <p>{info.Summary}</p>
  </header>
}
```

## 📄 PDF Generation

PDF generation requires Chrome or Chromium to be installed:

### Ubuntu/Debian
```bash
sudo apt install chromium-browser
```

### Arch Linux
```bash
sudo pacman -S chromium
```

### macOS
```bash
brew install --cask google-chrome
```

### Fallback
If Chrome/Chromium is not available, the application provides an HTML version with print instructions for manual PDF conversion.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Make your changes
4. Run tests and ensure everything works
5. Commit your changes (`git commit -m 'Add amazing feature'`)
6. Push to the branch (`git push origin feature/amazing-feature`)
7. Open a Pull Request

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [Templ](https://templ.guide/) - Type-safe templates for Go
- [Air](https://github.com/air-verse/air) - Live reloading for Go apps
- [ChromeDP](https://github.com/chromedp/chromedp) - Chrome DevTools Protocol for PDF generation
- [Font Awesome](https://fontawesome.com/) - Icons
- [Inter Font](https://rsms.me/inter/) - Typography

## 📞 Support

If you encounter any issues or have questions:

1. Check the [Issues](../../issues) page
2. Create a new issue with detailed information
3. Include steps to reproduce any bugs

---

**Happy CV building! 🎉**
